using Facepunch;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using Rust;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using System.Globalization;
using System.Text;

namespace Oxide.Plugins
{
    [Info("AwakenRoamBubble", "Skelee", "1.4.0")]
    [Description("Creates a dedicated PvP zone (Roam Bubble) at a random biome location, tracks kills, AKs, and M2 usage, displays a custom UI, and enforces weapon/attachment restrictions using Clans system. Enhanced Discord embeds with maze-style formatting.")]
    public class AutomaticRoamBubble : CovalencePlugin
    {
        #region Config
        public class UI
        {
            [JsonProperty(PropertyName = "Use UI")] public bool useUI;
            [JsonProperty(PropertyName = "UI Text")] public string UIText;
            [JsonProperty(PropertyName = "UI Text Color")] public string UITextColor;
            [JsonProperty(PropertyName = "UI Text Font Size")] public int UITextFontSize;
            [JsonProperty(PropertyName = "UI Background Color")] public string UIBackgroundColor;
            [JsonProperty(PropertyName = "Logo Image URL")] public string logoImageURL;
        }

        public class Messages
        {
            [JsonProperty(PropertyName = "Display Name for Chat Messages")] public string chatName;
            [JsonProperty(PropertyName = "Display Name Color for Chat Messages")] public string chatNameColor;
            [JsonProperty(PropertyName = "Steam ID Avatar for Chat Messages")] public ulong avatarSteamID;
            [JsonProperty(PropertyName = "Send Global Chat Message when roam starts?")] public bool sendChatMessageOnRoam;
            [JsonProperty(PropertyName = "Send End Result to Global Chat?")] public bool sendEndResultToChat;
            [JsonProperty(PropertyName = "Send End Result to Discord?")] public bool sendEndResultToDiscord;
        }

        public class Discord
        {
            [JsonProperty(PropertyName = "Use Discord Output?")] public bool useDiscord;
            [JsonProperty(PropertyName = "Discord Webhook URL")] public string discordWebhookURL;
            [JsonProperty(PropertyName = "Roam Winners Embed Title")] public string embedTitleWinners;
            [JsonProperty(PropertyName = "Roam Winners Embed Color")] public int embedColorWinners;
            [JsonProperty(PropertyName = "Roam No Winners Embed Title")] public string embedTitleNoWinners;
            [JsonProperty(PropertyName = "Roam No Winners Embed Color")] public int embedColorNoWinners;
            [JsonProperty(PropertyName = "Server Details")] public string serverDetails;
        }

        public class BiomeSettings
        {
            [JsonProperty(PropertyName = "Default Biome")] public string defaultBiome;
            [JsonProperty(PropertyName = "Allowed Biomes")] public List<string> allowedBiomes;
            [JsonProperty(PropertyName = "Snow Positions")] public List<Vector3> snowPositions = new List<Vector3>();
            [JsonProperty(PropertyName = "Desert Positions")] public List<Vector3> desertPositions = new List<Vector3>();
            [JsonProperty(PropertyName = "Grass Positions")] public List<Vector3> grassPositions = new List<Vector3>();
        }

        public class WeaponRestrictions
        {
            [JsonProperty(PropertyName = "Use Allowed Weapons List")] public bool useAllowedWeaponsList;
            [JsonProperty(PropertyName = "Allowed Weapons")] public Dictionary<string, string> allowedWeapons;
            [JsonProperty(PropertyName = "Blocked Weapons")] public Dictionary<string, string> blockedWeapons;
            [JsonProperty(PropertyName = "Blocked Attachments")] public Dictionary<string, string> blockedAttachments;
            [JsonProperty(PropertyName = "Blocked Ammunition")] public Dictionary<string, string> blockedAmmunition;
            [JsonProperty(PropertyName = "Blocked Explosives")] public Dictionary<string, string> blockedExplosives;
            [JsonProperty(PropertyName = "Allow All Other Weapons")] public bool allowOtherWeapons;
            [JsonProperty(PropertyName = "Allow All Other Attachments")] public bool allowOtherAttachments;
        }

        public class DecaySettings
        {
            [JsonProperty(PropertyName = "Enable Accelerated Decay")] public bool enableAcceleratedDecay;
            [JsonProperty(PropertyName = "Decay Speed Multiplier")] public float decaySpeedMultiplier;
            [JsonProperty(PropertyName = "Apply to All Structures")] public bool applyToAllStructures;
            [JsonProperty(PropertyName = "Apply to Barricades Only")] public bool applyToBarricadesOnly;
            [JsonProperty(PropertyName = "Decay Check Interval (seconds)")] public float decayCheckInterval;
        }

        public class HighWallDecaySettings
        {
            [JsonProperty(PropertyName = "Enable High Wall Decay (instead of instant removal)")]
            public bool EnableHighWallDecay { get; set; } = true;

            [JsonProperty(PropertyName = "High Wall Decay Duration (seconds to fully decay)")]
            public float HighWallDecayDuration { get; set; } = 30.0f;

            [JsonProperty(PropertyName = "High Wall Decay Tick Interval (seconds between decay ticks)")]
            public float HighWallDecayTickInterval { get; set; } = 2.0f;

            [JsonProperty(PropertyName = "Start Decay After Roam Ends (seconds delay)")]
            public float StartDecayAfterRoamEnds { get; set; } = 10.0f;

            [JsonProperty(PropertyName = "Show Decay Messages to Players")]
            public bool ShowDecayMessages { get; set; } = true;

            [JsonProperty(PropertyName = "Decay All Walls Simultaneously")]
            public bool DecayAllWallsSimultaneously { get; set; } = false;

            [JsonProperty(PropertyName = "Stagger Decay Start (seconds between each wall)")]
            public float StaggerDecayStart { get; set; } = 1.0f;
        }

        static Configuration config;
        public class Configuration
        {
            [JsonProperty(PropertyName = "Use Clans")] public bool useClans;
            [JsonProperty(PropertyName = "Sphere Radius")] public int sphereRadius;
            [JsonProperty(PropertyName = "Amount of Spheres (More = Darker, creates multiple sphere entities)")] public int numOfSpheres;
            [JsonProperty(PropertyName = "Message Settings")] public Messages messages;
            [JsonProperty(PropertyName = "UI Settings")] public UI ui;
            [JsonProperty(PropertyName = "Discord Settings")] public Discord discord;
            [JsonProperty(PropertyName = "Biome Settings")] public BiomeSettings biomeSettings;
            [JsonProperty(PropertyName = "Weapon Restrictions")] public WeaponRestrictions weaponRestrictions;
            [JsonProperty(PropertyName = "Decay Settings")] public DecaySettings decaySettings;
            [JsonProperty(PropertyName = "High Wall Decay Settings")] public HighWallDecaySettings highWallDecaySettings;
            [JsonProperty(PropertyName = "Roam Time")] public string roamTime;
            [JsonProperty(PropertyName = "Discord Webhook URL")] public string webhook;
            [JsonProperty(PropertyName = "Debug Mode")] public bool debug;
            [JsonProperty(PropertyName = "Enhanced Land Validation")] public bool enhancedLandValidation;
            [JsonProperty(PropertyName = "Roam Validation Interval (seconds)")] public float roamValidationInterval;


            public static Configuration DefaultConfig()
            {
                return new Configuration
                {
                    useClans = true,
                    sphereRadius = 150,
                    numOfSpheres = 7,
                    messages = new Messages
                    {
                        chatName = "Roam Bubble",
                        chatNameColor = "#d4af37",
                        avatarSteamID = 76561198194158447,
                        sendChatMessageOnRoam = true,
                        sendEndResultToChat = true,
                        sendEndResultToDiscord = true
                    },
                    ui = new UI
                    {
                        useUI = true,
                        UIText = "ROAMS",
                        UITextColor = "1 0.5 0 1",
                        UITextFontSize = 20,
                        UIBackgroundColor = "0 0 0 0.5",
                        logoImageURL = "https://cdn.awakenrust.com/oasis_roams.png"
                    },
                    discord = new Discord
                    {
                        useDiscord = true,
                        discordWebhookURL = "",
                        embedTitleWinners = "Awaken Roams - {0}x | No BPs | Kits | Shop",
                        embedColorWinners = 3066993,
                        embedTitleNoWinners = "Awaken Roams - No Winners",
                        embedColorNoWinners = 15158332,
                        serverDetails = "US 10x | No BPs | Kits | Shop"
                    },
                    biomeSettings = new BiomeSettings
                    {
                        defaultBiome = "Grass",
                        allowedBiomes = new List<string> { "Snow", "Desert", "Grass" },
                        snowPositions = new List<Vector3>(),
                        desertPositions = new List<Vector3>(),
                        grassPositions = new List<Vector3>()
                    },
                    weaponRestrictions = new WeaponRestrictions
                    {
                        useAllowedWeaponsList = false,
                        allowedWeapons = new Dictionary<string, string>
                        {
                            { "rifle.ak", "AK47" },
                            { "rifle.lr300", "LR-300 Assault Rifle" },
                            { "lmg.m249", "M249" },
                            { "smg.mp5", "MP5A4" },
                            { "smg.custom", "Custom SMG" },
                            { "smg.thompson", "Thompson" },
                            { "pistol.m92", "M92 Pistol" },
                            { "pistol.python", "Python Revolver" },
                            { "pistol.revolver", "Revolver" },
                            { "pistol.semiauto", "Semi-Automatic Pistol" },
                            { "shotgun.spas12", "SPAS-12 Shotgun" },
                            { "crossbow", "Crossbow" },
                            { "bow.hunting", "Hunting Bow" },
                            { "bow.compound", "Compound Bow" }
                        },
                        blockedWeapons = new Dictionary<string, string>
                        {
                            { "rifle.bolt", "Bolt Action Rifle" },
                            { "rifle.l96", "L96 Rifle" },
                            { "shotgun.m4", "M4 Shotgun" },
                            { "shotgun.waterpipe", "Waterpipe Shotgun" },
                            { "shotgun.double", "Double Barrel Shotgun" },
                            { "shotgun.pump", "Pump Shotgun" },
                            { "lmg.m249", "M249" },
                            { "multiplegrenadelauncher", "Multiple Grenade Launcher" },
                            { "rocket.launcher", "Rocket Launcher" }
                        },
                        blockedAttachments = new Dictionary<string, string>
                        {
                            { "weapon.mod.silencer", "Silencer" }
                        },
                        blockedAmmunition = new Dictionary<string, string>(),
                        blockedExplosives = new Dictionary<string, string>(),
                        allowOtherWeapons = true,
                        allowOtherAttachments = true
                    },
                    decaySettings = new DecaySettings
                    {
                        enableAcceleratedDecay = true,
                        decaySpeedMultiplier = 5.0f,
                        applyToAllStructures = true,
                        applyToBarricadesOnly = false,
                        decayCheckInterval = 30.0f
                    },
                    highWallDecaySettings = new HighWallDecaySettings
                    {
                        EnableHighWallDecay = true,
                        HighWallDecayDuration = 30.0f,
                        HighWallDecayTickInterval = 2.0f,
                        StartDecayAfterRoamEnds = 10.0f,
                        ShowDecayMessages = true,
                        DecayAllWallsSimultaneously = false,
                        StaggerDecayStart = 1.0f
                    },
                    webhook = "REPLACE_WITH_YOUR_DISCORD_WEBHOOK_URL",
                    roamTime = "30m",
                    debug = false,
                    enhancedLandValidation = true,
                    roamValidationInterval = 60f,

                };
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                PrintError($"Error loading config: {e.Message}");
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = Configuration.DefaultConfig();
        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Data File for Player Spawns
        private class PlayerSpawnData
        {
            public Dictionary<string, List<Vector3>> BiomePlayerSpawns { get; set; } = new Dictionary<string, List<Vector3>>
            {
                {"snow", new List<Vector3>()},
                {"desert", new List<Vector3>()},
                {"grass", new List<Vector3>()}
            };
        }

        private PlayerSpawnData playerSpawnData;
        private const string PlayerSpawnDataFile = "automaticroambubble_playerspawns";

        private void LoadPlayerSpawnData()
        {
            playerSpawnData = Interface.Oxide.DataFileSystem.ReadObject<PlayerSpawnData>(PlayerSpawnDataFile);
            if (playerSpawnData == null || playerSpawnData.BiomePlayerSpawns == null)
            {
                playerSpawnData = new PlayerSpawnData();
                SavePlayerSpawnData();
            }
        }

        private void SavePlayerSpawnData()
        {
            Interface.Oxide.DataFileSystem.WriteObject(PlayerSpawnDataFile, playerSpawnData);
        }
        #endregion

        #region State Persistence for Reload/Unload
        private class RoamState
        {
            public List<RoamData> ActiveRoams { get; set; } = new List<RoamData>();
            public DateTime SaveTime { get; set; }
        }

        private class RoamData
        {
            public Vector3 Position { get; set; }
            public string Biome { get; set; }
            public float TimeRemaining { get; set; }
            public float InitialTime { get; set; }
            public int Radius { get; set; }
            public Dictionary<string, TeamStats> TeamStats { get; set; } = new Dictionary<string, TeamStats>();
            public Dictionary<string, PlayerStats> PlayerStats { get; set; } = new Dictionary<string, PlayerStats>();
        }

        private const string RoamStateFile = "automaticroambubble_state";

        private void SaveActiveRoamState()
        {
            if (activeRoams.Count == 0) return;

            var roamState = new RoamState
            {
                SaveTime = DateTime.UtcNow
            };

            foreach (var roam in activeRoams)
            {
                if (roam == null) continue;

                var roamData = new RoamData
                {
                    Position = roam.transform.position,
                    Biome = roam.biome,
                    TimeRemaining = roam.roamTime,
                    InitialTime = roam.initialRoamTime,
                    Radius = config.sphereRadius,
                    TeamStats = new Dictionary<string, TeamStats>(roam.teamStats),
                    PlayerStats = new Dictionary<string, PlayerStats>(roam.playerStats)
                };

                roamState.ActiveRoams.Add(roamData);
            }

            Interface.Oxide.DataFileSystem.WriteObject(RoamStateFile, roamState);
            Puts($"Saved state for {roamState.ActiveRoams.Count} active roam(s)");
        }

        private void RestoreActiveRoamState()
        {
            var roamState = Interface.Oxide.DataFileSystem.ReadObject<RoamState>(RoamStateFile);
            if (roamState?.ActiveRoams == null || roamState.ActiveRoams.Count == 0) return;

            // Check if the save is recent (within 5 minutes)
            var timeSinceSave = DateTime.UtcNow - roamState.SaveTime;
            if (timeSinceSave.TotalMinutes > 5)
            {
                Puts("Roam state is too old, not restoring");
                Interface.Oxide.DataFileSystem.WriteObject(RoamStateFile, new RoamState()); // Clear old state
                return;
            }

            Puts($"Restoring {roamState.ActiveRoams.Count} roam(s) from saved state...");

            foreach (var roamData in roamState.ActiveRoams)
            {
                // Adjust time remaining based on how long the plugin was unloaded
                float adjustedTime = roamData.TimeRemaining - (float)timeSinceSave.TotalSeconds;
                if (adjustedTime <= 0)
                {
                    Puts($"Roam at {roamData.Position} expired while plugin was unloaded, skipping");
                    continue;
                }

                // Recreate the roam bubble
                var bubbleObject = new GameObject();
                var bubbleComp = bubbleObject.AddComponent<AutomaticRoamBubbleComp>();
                bubbleComp.CreateBubble(roamData.Position, roamData.Radius, roamData.Biome, (int)adjustedTime);

                // Restore stats
                bubbleComp.teamStats = roamData.TeamStats;
                bubbleComp.playerStats = roamData.PlayerStats;
                bubbleComp.initialRoamTime = roamData.InitialTime;

                Puts($"Restored roam in {roamData.Biome} biome with {adjustedTime:F0} seconds remaining");
            }

            // Clear the saved state
            Interface.Oxide.DataFileSystem.WriteObject(RoamStateFile, new RoamState());
        }
        #endregion

        #region Local Image Caching
        private string cachedLogoUrl = null;
        private bool isDownloadingLogo = false;

        private void LoadImages()
        {
            // Cache the logo locally to prevent glitching
            CacheLogoLocally();
        }

        private void CacheLogoLocally()
        {
            if (string.IsNullOrEmpty(config?.ui?.logoImageURL) || isDownloadingLogo)
                return;

            string logoUrl = config.ui.logoImageURL;

            // Skip if already cached
            if (!string.IsNullOrEmpty(cachedLogoUrl))
                return;

            // Validate URL
            if (!logoUrl.StartsWith("http://") && !logoUrl.StartsWith("https://"))
                return;

            if (logoUrl.Contains("<color") || logoUrl.Contains("</color>"))
                return;

            isDownloadingLogo = true;

            // Download and cache the image
            webrequest.Enqueue(logoUrl, null, (code, response) =>
            {
                isDownloadingLogo = false;

                if (code != 200 || string.IsNullOrEmpty(response))
                {
                    PrintWarning($"[Roam Logo] Failed to download logo from {logoUrl} - Code: {code}");
                    return;
                }

                try
                {
                    // For now, just use the original URL since base64 conversion is complex in Oxide
                    // The caching will prevent repeated downloads by storing the URL
                    cachedLogoUrl = logoUrl;

                    // Update UI for all players currently in bubbles
                    RefreshUIForAllPlayers();
                }
                catch (Exception ex)
                {
                    PrintError($"[Roam Logo] Error caching logo: {ex.Message}");
                }

            }, this, Core.Libraries.RequestMethod.GET);
        }

        private void RefreshUIForAllPlayers()
        {
            foreach (var kvp in playersInBubble)
            {
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player != null && player.IsConnected)
                {
                    // Recreate UI with cached logo
                    CreateRoamUI(player);
                }
            }
        }

        private void CreateLogo(CuiElementContainer container, string parentName)
        {
            // Use cached logo if available, otherwise fall back to direct URL
            string logoUrl = !string.IsNullOrEmpty(cachedLogoUrl) ? cachedLogoUrl : config?.ui?.logoImageURL;

            if (string.IsNullOrEmpty(logoUrl))
            {
                if (config.debug)
                    Puts("[Roam Logo] No logo URL configured or cached");
                return;
            }

            // Validate that the URL is actually a valid image URL and not HTML color tags
            if (logoUrl.Contains("<color") || logoUrl.Contains("</color>"))
            {
                PrintWarning($"[Roam Logo] LogoImageURL contains HTML color tags instead of a valid image URL: {logoUrl}");
                return;
            }

            // Create logo element with cached or direct URL
            if (logoUrl.StartsWith("http://") || logoUrl.StartsWith("https://"))
            {
                if (config.debug)
                    Puts($"[Roam Logo] Creating logo element - Using URL: {logoUrl}");

                container.Add(new CuiElement
                {
                    Name = "roam_logo_image",
                    Parent = parentName,
                    Components =
                    {
                        new CuiRawImageComponent { Url = logoUrl },
                        new CuiRectTransformComponent
                        {
                            AnchorMin = "0.0 0.3",
                            AnchorMax = "0.8 1.0",
                            OffsetMin = "0 0",
                            OffsetMax = "0 0"
                        }
                    }
                });
            }
            else
            {
                if (config.debug)
                    Puts($"[Roam Logo] Invalid logo URL format: {logoUrl}");
            }
        }
        #endregion

        #region Defines
        public static AutomaticRoamBubble Instance;
        [PluginReference] private Plugin? AwakenVotingSystem;
        [PluginReference] private Plugin? Clans;
        [PluginReference] private Plugin? ClanCores;
        [PluginReference] private Plugin? AwakenStats;

        [PluginReference] private Plugin? Vanish;
        [PluginReference] private Plugin? AdminRadar;
        private const string CallRoamPermission = "automaticroambubble.callroam";
        private const string SetLocationPermission = "automaticroambubble.setlocation";
        private List<AutomaticRoamBubbleComp> activeRoams = new List<AutomaticRoamBubbleComp>();
        private Dictionary<string, AutomaticRoamBubbleComp> playersInBubble = new Dictionary<string, AutomaticRoamBubbleComp>();
        private HashSet<ItemId> trackedWeaponItemUids = new HashSet<ItemId>();
        private HashSet<ulong> convertedHighWalls = new HashSet<ulong>(); // Track converted high walls for decay

        // Enhanced decay system for converted high walls (based on maze plugin)
        private class DecayingHighWall
        {
            public BaseEntity Entity { get; set; }
            public float MaxHealth { get; set; }
            public float CurrentHealth { get; set; }
            public Timer DecayTimer { get; set; }
            public float DecayStartTime { get; set; }
            public float DecayDuration { get; set; } = 30.0f; // Default 30 seconds to fully decay
            public float TickInterval { get; set; } = 2.0f; // Decay every 2 seconds
        }

        private Dictionary<ulong, DecayingHighWall> decayingHighWalls = new Dictionary<ulong, DecayingHighWall>();
        #endregion

        #region Language
        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["NoPermission"] = "You don't have permission to call roams.",
                ["RoamStartedChat"] = "A roam has started! Use /roams to teleport.",
                ["RoamEndedNoWinners"] = "Roam is over, there were no winners due to no participants.",
                ["RoamEndedWinners"] = "Roam is over, {0}'s Team have won the roam.\n\nMembers:\n{1}\nTotal Kills: {2}\nAK Kills: {3}\nM2 Kills: {4}\nTotal Deaths: {5}\nTotal Damage: {6}\nTotal Headshots: {7}",
                ["WeaponRestricted"] = "You cannot use {0} in the roam! This weapon is blocked.",
                ["AttachmentRestricted"] = "You cannot use {0} attachment in the roam! This attachment is blocked.",
                ["AmmunitionRestricted"] = "You cannot use {0} ammunition in the roam! This ammunition is blocked.",
                ["ExplosiveRestricted"] = "You cannot use {0} in the roam! This explosive item is blocked.",
                ["AlreadyInRoam"] = "You are already in a roam bubble!",
                ["LocationSet"] = "Roam Bubble location for {0} biome set to your current position: {1}",
                ["SpawnSet"] = "Roam spawn location for {0} biome set to your current position: {1}",
                ["InvalidBiome"] = "Invalid biome. Use: {0}",
                ["SpawnsCleared"] = "Cleared {0} spawn points from {1} biome.",
                ["AllSpawnsCleared"] = "Cleared all {0} spawn points from all biomes."
            }, this);
        }
        #endregion

        #region Hooks


        private void CleanupLeftoverBubbles()
        {
            var existingBubbles = UnityEngine.Object.FindObjectsOfType<AutomaticRoamBubbleComp>();
            if (existingBubbles.Length > 0)
            {
                Puts($"AutomaticRoamBubble: Found {existingBubbles.Length} leftover roam bubbles from previous plugin instance. Cleaning up...");

                foreach (var bubble in existingBubbles)
                {
                    try
                    {
                        if (bubble != null)
                        {
                            bubble.DeleteCircle();
                            UnityEngine.Object.DestroyImmediate(bubble);
                        }
                    }
                    catch (System.Exception ex)
                    {
                        PrintError($"Error cleaning up leftover bubble: {ex.Message}");
                    }
                }

                foreach (BasePlayer player in BasePlayer.activePlayerList)
                {
                    if (player != null && player.IsConnected)
                    {
                        CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                    }
                }

                Puts("AutomaticRoamBubble: Leftover bubble cleanup complete.");
            }
        }

        void OnServerInitialized()
        {
            if (Clans == null)
            {
                PrintWarning("Clans plugin not found! Players will be treated as solo players.");
                PrintWarning("Make sure Clans plugin is installed and loaded for proper clan functionality.");
            }
            else
            {
                Puts("Clans plugin detected and integrated successfully.");
            }
        }

        void Unload()
        {
            Puts("AutomaticRoamBubble: Plugin unloading - saving state and cleaning up...");

            // Save current roam state before cleanup
            SaveActiveRoamState();

            CleanupAllPlayerUIs();

            CleanupAllRoamBubbles();

            playersInBubble.Clear();
            trackedWeaponItemUids.Clear();
            convertedHighWalls.Clear(); // Clear converted high walls tracking
            CleanupAllDecayingHighWalls(); // Clean up decay system

            playerCache.Clear();
            cacheTimestamps.Clear();

            Puts($"AutomaticRoamBubble: Cleanup complete. Removed {activeRoams.Count} active roam bubbles.");
        }

        void Loaded()
        {
            Instance = this;
            permission.RegisterPermission(CallRoamPermission, this);
            permission.RegisterPermission(SetLocationPermission, this);
            LoadPlayerSpawnData();
            LoadImages(); // This will now cache the logo locally to prevent glitching

            CleanupLeftoverBubbles();

            // Restore any saved roam state after a short delay
            timer.Once(2f, () => {
                RestoreActiveRoamState();
            });

            // Start periodic roam validation timer (configurable interval)
            timer.Every(config.roamValidationInterval, () => ValidateActiveRoams());

            // Additional aggressive validation every 30 seconds for critical issues
            timer.Every(30f, () => AggressiveRoamValidation());
        }

        private void CleanupAllPlayerUIs()
        {
            foreach (var kvp in playersInBubble.ToList())
            {
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player != null && player.IsConnected)
                {
                    CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                }
            }
        }

        private void CleanupAllRoamBubbles()
        {
            // Clean up all active roam bubbles with proper cleanup
            foreach (AutomaticRoamBubbleComp roamBubble in activeRoams.ToList())
            {
                if (roamBubble != null)
                {
                    try
                    {
                        // Call the bubble's cleanup method first
                        roamBubble.DeleteCircle();

                        // Then destroy the component
                        UnityEngine.Object.DestroyImmediate(roamBubble);
                    }
                    catch (System.Exception ex)
                    {
                        PrintError($"Error cleaning up roam bubble: {ex.Message}");
                    }
                }
            }

            // Clear the list
            activeRoams.Clear();
        }

        private static Dictionary<string, BasePlayer> playerCache = new Dictionary<string, BasePlayer>();
        private static readonly TimeSpan cacheTTL = TimeSpan.FromSeconds(5);
        private static Dictionary<string, DateTime> cacheTimestamps = new Dictionary<string, DateTime>();

        object OnEntityTakeDamage(BasePlayer victim, HitInfo info)
        {
            BasePlayer attacker = info?.InitiatorPlayer;
            if (attacker == null) return null;

            AutomaticRoamBubbleComp roamBubble;
            if (!Instance.playersInBubble.TryGetValue(attacker.UserIDString, out roamBubble)) return null;

            AutomaticRoamBubbleComp roamBubbleToCompare;
            if (!Instance.playersInBubble.TryGetValue(victim.UserIDString, out roamBubbleToCompare)) return null;


            if (!IsWeaponAllowed(info.Weapon) || !AreAttachmentsAllowed(info.Weapon))
            {
                attacker.ChatMessage(string.Format(Instance.lang.GetMessage("WeaponRestricted", Instance), info.Weapon?.ShortPrefabName ?? "unknown weapon"));
                return true;
            }

            TeamStats teamStats;
            string attackersTeam = GetTeamTag(attacker);

            if (!string.IsNullOrEmpty(attackersTeam))
            {
                if (!roamBubble.teamStats.TryGetValue(attackersTeam, out teamStats))
                {
                    List<string> members = GetTeamMembers(attacker);
                    if (members.Contains(victim.UserIDString)) return null;

                    roamBubble.teamStats.Add(attackersTeam, new TeamStats
                    {
                        members = members,
                        kills = 0,
                        deaths = 0,
                        headshots = info.isHeadshot ? 1 : 0,
                        damage = Convert.ToInt32(info.damageTypes.Total()),
                        akKills = 0,
                        m2Kills = 0
                    });
                }
                else
                {
                    if (teamStats.members.Contains(victim.UserIDString)) return null;
                    if (info.isHeadshot) teamStats.headshots += 1;
                    teamStats.damage += Convert.ToInt32(info.damageTypes.Total());

                }
            }


            if (roamBubble.playerStats.ContainsKey(attacker.UserIDString))
            {
                if (info.isHeadshot)
                    roamBubble.playerStats[attacker.UserIDString].headshots += 1;
                roamBubble.playerStats[attacker.UserIDString].damage += Convert.ToInt32(info.damageTypes.Total());
            }

            return null;
        }

        void OnEntityDeath(BasePlayer victim, HitInfo info)
        {
            BasePlayer attacker = info?.InitiatorPlayer;
            if (attacker == null || !attacker.userID.IsSteamId()) return;

            // Check if either the victim OR attacker is in a roam bubble
            AutomaticRoamBubbleComp roamBubble = null;
            bool victimInBubble = playersInBubble.TryGetValue(victim.UserIDString, out roamBubble);
            bool attackerInBubble = playersInBubble.TryGetValue(attacker.UserIDString, out var attackerBubble);

            // If neither player is in a roam bubble, ignore this death
            if (!victimInBubble && !attackerInBubble) return;

            // Use the bubble from whichever player is in one (prefer victim's bubble if both are in bubbles)
            if (!victimInBubble && attackerInBubble)
                roamBubble = attackerBubble;

            if (roamBubble == null) return;

            // Check weapon and attachment restrictions
            if (!IsWeaponAllowed(info.Weapon) || !AreAttachmentsAllowed(info.Weapon))
            {
                if (config.debug)
                    Puts($"[Roam Debug] Kill ignored due to restricted weapon: {info.Weapon?.ShortPrefabName ?? "unknown"}");
                return; // Ignore kill if weapon/attachment is restricted
            }

            if (config.debug)
                Puts($"[Roam Debug] Processing kill: {attacker.displayName} killed {victim.displayName} with {info.Weapon?.ShortPrefabName ?? "unknown"}");

            TeamStats teamStats;
            string attackersTeam = GetTeamTag(attacker);
            if (!string.IsNullOrEmpty(attackersTeam))
            {
                if (!roamBubble.teamStats.TryGetValue(attackersTeam, out teamStats))
                {
                    List<string> members = GetTeamMembers(attacker);
                    if (members.Contains(victim.UserIDString)) return; // Don't track friendly fire kills

                    roamBubble.teamStats.Add(attackersTeam, new TeamStats
                    {
                        members = members,
                        kills = 1,
                        deaths = 0,
                        headshots = info.isHeadshot ? 1 : 0,
                        damage = Convert.ToInt32(info.damageTypes.Total()),
                        akKills = IsWeaponAK(info.Weapon) ? 1 : 0,
                        m2Kills = IsWeaponM2(info.Weapon) ? 1 : 0
                    });
                }
                else
                {
                    if (teamStats.members.Contains(victim.UserIDString)) return; // Don't track friendly fire kills

                    teamStats.kills += 1;
                    if (info.isHeadshot) teamStats.headshots += 1;
                    teamStats.damage += Convert.ToInt32(info.damageTypes.Total());
                    if (IsWeaponAK(info.Weapon)) teamStats.akKills += 1;
                    if (IsWeaponM2(info.Weapon)) teamStats.m2Kills += 1;
                }
            }

            // Update player stats for the attacker (initialize if needed)
            if (!roamBubble.playerStats.ContainsKey(attacker.UserIDString))
            {
                roamBubble.playerStats[attacker.UserIDString] = new PlayerStats();
                if (config.debug)
                    Puts($"[Roam Debug] Initialized player stats for attacker: {attacker.displayName}");
            }

            roamBubble.playerStats[attacker.UserIDString].kills += 1;
            if (info.isHeadshot)
                roamBubble.playerStats[attacker.UserIDString].headshots += 1;
            roamBubble.playerStats[attacker.UserIDString].damage += Convert.ToInt32(info.damageTypes.Total());

            if (config.debug)
                Puts($"[Roam Debug] Updated attacker stats: {attacker.displayName} - Kills: {roamBubble.playerStats[attacker.UserIDString].kills}, Deaths: {roamBubble.playerStats[attacker.UserIDString].deaths}");

            string victimsTeam = GetTeamTag(victim);
            if (!string.IsNullOrEmpty(victimsTeam))
            {
                if (!roamBubble.teamStats.TryGetValue(victimsTeam, out teamStats))
                {
                    List<string> members = GetTeamMembers(victim);
                    roamBubble.teamStats.Add(victimsTeam, new TeamStats
                    {
                        members = members,
                        kills = 0,
                        deaths = 1,
                        headshots = 0,
                        damage = 0,
                        akKills = 0,
                        m2Kills = 0
                    });
                }
                else
                    teamStats.deaths += 1;
            }

            // Update player stats for the victim (death) - initialize if needed
            if (!roamBubble.playerStats.ContainsKey(victim.UserIDString))
            {
                roamBubble.playerStats[victim.UserIDString] = new PlayerStats();
                if (config.debug)
                    Puts($"[Roam Debug] Initialized player stats for victim: {victim.displayName}");
            }

            roamBubble.playerStats[victim.UserIDString].deaths += 1;

            if (config.debug)
                Puts($"[Roam Debug] Updated victim stats: {victim.displayName} - Kills: {roamBubble.playerStats[victim.UserIDString].kills}, Deaths: {roamBubble.playerStats[victim.UserIDString].deaths}");

            if (playersInBubble.ContainsKey(victim.UserIDString))
            {
                playersInBubble.Remove(victim.UserIDString);
                CuiHelper.DestroyUi(victim, "AwakenRoamsUI");
            }
        }

        void OnPlayerDeath(BasePlayer player, HitInfo info)
        {
            // Handle all player deaths (not just PvP kills)
            if (player == null) return;

            // Remove player from roam bubble if they're in one
            if (playersInBubble.ContainsKey(player.UserIDString))
            {
                playersInBubble.Remove(player.UserIDString);
                CuiHelper.DestroyUi(player, "AwakenRoamsUI");

                if (config?.debug == true)
                    Puts($"[Roam Debug] Removed {player.displayName} from roam bubble due to death");
            }
        }

        void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            // Handle players disconnecting while in a roam
            if (player == null) return;

            // Remove player from roam bubble if they're in one
            if (playersInBubble.ContainsKey(player.UserIDString))
            {
                playersInBubble.Remove(player.UserIDString);

                if (config?.debug == true)
                    Puts($"[Roam Debug] Removed {player.displayName} from roam bubble due to disconnect");
            }
        }

        object CanUseAttachment(Item item, BasePlayer player)
        {
            if (!playersInBubble.ContainsKey(player.UserIDString)) return null;
            if (!IsAttachmentAllowed(item.info.shortname))
            {
                player.ChatMessage(string.Format(lang.GetMessage("AttachmentRestricted", this), item.info.shortname));
                return false;
            }
            return null;
        }

        object CanUseItem(BasePlayer player, Item item)
        {
            if (!playersInBubble.ContainsKey(player.UserIDString)) return null;

            // Check ammunition
            if (item.info.category == ItemCategory.Ammunition && !IsAmmunitionAllowed(item.info.shortname))
            {
                player.ChatMessage(string.Format(lang.GetMessage("AmmunitionRestricted", this), item.info.shortname));
                return false;
            }

            // Check explosives
            if (!IsExplosiveAllowed(item.info.shortname))
            {
                player.ChatMessage(string.Format(lang.GetMessage("ExplosiveRestricted", this), item.info.shortname));
                return false;
            }

            return null;
        }

        object CanThrowExplosive(BasePlayer player, BaseEntity explosive)
        {
            if (!playersInBubble.ContainsKey(player.UserIDString)) return null;

            if (explosive != null && !IsExplosiveAllowed(explosive.ShortPrefabName))
            {
                player.ChatMessage(string.Format(lang.GetMessage("ExplosiveRestricted", this), explosive.ShortPrefabName));
                return false;
            }

            return null;
        }

        void OnPlayerActiveItemChanged(BasePlayer player, Item oldItem, Item newItem)
        {
            if (player == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(player.UserIDString)) return;

            // If the new item is a weapon, check if it's allowed
            if (newItem?.info?.category == ItemCategory.Weapon)
            {
                if (!IsWeaponAllowedByShortname(newItem.info.shortname))
                {
                    // Force unequip the weapon immediately
                    NextTick(() =>
                    {
                        if (player != null && player.IsConnected && newItem != null)
                        {
                            // Clear the active item to prevent equipping
                            player.svActiveItemID = new ItemId(0);
                            player.SendNetworkUpdate();
                            SendMessage(player, "WeaponRestricted", newItem.info.displayName.english);
                        }
                    });
                    return;
                }

                // Check attachments on the weapon
                if (!AreAttachmentsAllowed(newItem.GetHeldEntity()))
                {
                    NextTick(() =>
                    {
                        if (player != null && player.IsConnected && newItem != null)
                        {
                            // Clear the active item to prevent equipping
                            player.svActiveItemID = new ItemId(0);
                            player.SendNetworkUpdate();
                            SendMessage(player, "AttachmentRestricted", "weapon attachment");
                        }
                    });
                }
            }
        }

        object CanDeployItem(BasePlayer player, Deployer deployer, uint entityId)
        {
            if (!playersInBubble.ContainsKey(player.UserIDString)) return null;

            if (deployer?.GetItem()?.info?.GetComponent<ItemModDeployable>()?.entityPrefab?.resourcePath != null)
            {
                string prefabPath = deployer.GetItem().info.GetComponent<ItemModDeployable>().entityPrefab.resourcePath.ToLower();
                string itemShortname = deployer.GetItem().info.shortname.ToLower();

                // Debug logging
                Puts($"[Roam Debug] CanDeployItem - Item: {itemShortname}, PrefabPath: {prefabPath}");

                // Check for barricade by item shortname and prefab path
                if (itemShortname.Contains("barricade.wood.cover_double") ||
                    prefabPath.Contains("barricade.wood.cover_double") ||
                    prefabPath.Contains("barricade_cover_wood_double") ||
                    prefabPath.Contains("barricade.cover.wood_double"))
                {
                    Puts($"[Roam Debug] Allowing barricade deployment: {itemShortname}");
                    return null; // Allow barricade deployment
                }
                else
                {
                    Puts($"[Roam Debug] Blocking deployment: {itemShortname}");
                    player.ChatMessage("Only barricades can be deployed in the roam bubble! All other deployables are blocked.");
                    return false; // Block all other deployables
                }
            }

            return null;
        }

        object CanBuild(Planner planner, Construction prefab, Construction.Target target)
        {
            BasePlayer player = planner.GetOwnerPlayer();
            if (player == null) return null;

            // Check if the player is in a roam bubble
            if (Instance.playersInBubble.TryGetValue(player.UserIDString, out AutomaticRoamBubbleComp roamBubble))
            {
                string prefabShortName = prefab.fullName.ToLower();

                // Debug logging
                Puts($"[Roam Debug] CanBuild - FullName: {prefab.fullName}, ShortName: {prefabShortName}");

                // Check for barricade by multiple possible identifiers
                if (prefabShortName.Contains("barricade.cover.wood_double") ||
                    prefabShortName.Contains("barricade.wood.cover_double") ||
                    prefabShortName.Contains("barricade_cover_wood_double"))
                {
                    Puts($"[Roam Debug] Allowing barricade build: {prefabShortName}");
                    return null; // Allow barricade placement only
                }
                else
                {
                    Puts($"[Roam Debug] Blocking build: {prefabShortName}");
                    // Block ALL other structures in the roam bubble (including sleeping bags)
                    player.ChatMessage("Only barricades can be placed in the roam bubble! All other building is blocked.");
                    return false; // Prevent all other builds
                }
            }

            return null; // Allow builds outside the roam bubble
        }

        void OnEntityBuilt(Planner planner, GameObject go)
        {
            BasePlayer player = planner.GetOwnerPlayer();
            if (player == null) return;

            // Check if the player is in a roam bubble
            if (Instance.playersInBubble.TryGetValue(player.UserIDString, out AutomaticRoamBubbleComp roamBubble))
            {
                BaseEntity entity = go.GetComponent<BaseEntity>();
                if (entity == null) return;

                string prefabName = entity.PrefabName;
                string prefabShortName = entity.ShortPrefabName;

                // Also check the item that was used to place this entity
                Item activeItem = player.GetActiveItem();
                string itemShortname = activeItem?.info?.shortname ?? "unknown";

                // Debug logging to see what's being placed
                Puts($"Entity built in roam bubble: {prefabName} | Short: {prefabShortName} | Item: {itemShortname} | Player: {player.displayName}");

                // Additional debug logging for barricade detection (updated for correct prefab)
                bool isBarricadeByPrefab = prefabName.Contains("barricade.cover.wood_double") || prefabName.Contains("barricade.wood.cover_double");
                bool isBarricadeByShort = prefabShortName.Contains("barricade.cover.wood_double") || prefabShortName.Contains("barricade.wood.cover_double");
                bool isBarricadeByItem = itemShortname.Contains("barricade.cover.wood_double") || itemShortname.Contains("barricade.wood.cover_double");
                Puts($"Barricade detection: PrefabMatch={isBarricadeByPrefab}, ShortMatch={isBarricadeByShort}, ItemMatch={isBarricadeByItem}");

                // Check if the placed entity is a wooden barricade cover (check multiple possible names)
                if (isBarricadeByPrefab || isBarricadeByShort || isBarricadeByItem)
                {
                    try
                    {
                        Vector3 position = entity.transform.position;
                        Quaternion rotation = entity.transform.rotation;

                        // Allow conversion regardless of rotation - players should be able to place walls at any angle
                        if (config.debug)
                            Puts($"[Roam Debug] Barricade rotation: {rotation.eulerAngles} - allowing conversion");

                        // Reduce player proximity check to only 1 meter to allow more conversions
                        if (IsPlayerInSpawnArea(position, 1.0f))
                        {
                            if (config.debug)
                                Puts($"[Roam Debug] Player too close (within 1m) - keeping as normal barricade");
                            return; // Keep as normal barricade if player would be inside
                        }

                        // Rotate the high wall 180 degrees from the barricade's rotation
                        Quaternion rotatedRotation = rotation * Quaternion.Euler(0, 180, 0);

                        if (config.debug)
                            Puts($"Barricade detected! Position: {position}, Original Rotation: {rotation}, Rotated: {rotatedRotation}");

                        // Spawn a wooden high wall instead (matching the large wall in the screenshot)
                        var wallPrefab = "assets/prefabs/building/wall.external.high.wood/wall.external.high.wood.prefab";
                        BaseEntity highWall = GameManager.server.CreateEntity(wallPrefab, position, rotatedRotation, true);
                        if (highWall != null)
                        {
                            // Set the player as the owner of the high wall
                            highWall.OwnerID = player.userID;

                            highWall.Spawn();
                            highWall.SetParent(null); // Ensure it’s not parented to anything
                            highWall.SendNetworkUpdate(); // Update clients

                            // Track this converted high wall for decay system
                            convertedHighWalls.Add(highWall.net.ID.Value);

                            Puts($"Successfully spawned high wall at {position} for {player.displayName} (Owner: {player.userID}) - Added to decay tracking");

                            entity.Kill();
                        }
                        else
                        {
                            PrintError($"Failed to create high wall entity for {player.displayName}");
                            player.ChatMessage("❌ Failed to replace barricade with high wall.");
                        }
                    }
                    catch (System.Exception ex)
                    {
                        PrintError($"Error replacing barricade with high wall: {ex.Message}");
                        player.ChatMessage("❌ Error occurred while replacing barricade.");
                    }
                }
                // Sleeping bags are already allowed and don’t need replacement
            }
        }

        private void ReplaceBarricadeWithHighWall(BaseEntity barricade, Vector3 position, Quaternion rotation, BasePlayer player)
        {
            try
            {
                // Allow conversion regardless of rotation - players should be able to place walls at any angle
                if (config.debug)
                    Puts($"[Roam Debug] Barricade rotation: {rotation.eulerAngles} - allowing conversion");

                // Reduce player proximity check to only 1 meter to allow more conversions
                if (IsPlayerInSpawnArea(position, 1.0f))
                {
                    if (config.debug)
                        Puts($"[Roam Debug] Player too close (within 1m) - keeping as normal barricade");
                    return; // Keep as normal barricade if player collision
                }

                // Rotate the high wall 180 degrees from the barricade's rotation
                Quaternion rotatedRotation = rotation * Quaternion.Euler(0, 180, 0);

                // Spawn a wooden high wall instead
                var wallPrefab = "assets/prefabs/building/wall.external.high.wood/wall.external.high.wood.prefab";
                BaseEntity highWall = GameManager.server.CreateEntity(wallPrefab, position, rotatedRotation, true);

                if (highWall != null)
                {
                    // Set the player as the owner of the high wall
                    highWall.OwnerID = player.userID;

                    highWall.Spawn();
                    highWall.SetParent(null); // Ensure it's not parented to anything
                    highWall.SendNetworkUpdate(); // Update clients

                    // Track this converted high wall for decay system
                    Instance.convertedHighWalls.Add(highWall.net.ID.Value);

                    Puts($"Successfully replaced barricade with high wall at {position} for player {player.displayName} (Owner: {player.userID}) - Added to decay tracking");

                    // Destroy the placed barricade cover
                    barricade.Kill();
                }
                else
                {
                    PrintError($"Failed to create high wall entity for player {player.displayName}");
                }
            }
            catch (System.Exception ex)
            {
                PrintError($"Error replacing barricade with high wall: {ex.Message}");
            }
        }

        // Helper method to check if barricade is rotated from default orientation
        private bool IsBarricadeRotated(Quaternion rotation)
        {
            // Get the Y rotation angle (yaw)
            float yRotation = rotation.eulerAngles.y;

            // Normalize angle to 0-360 range
            while (yRotation < 0) yRotation += 360;
            while (yRotation >= 360) yRotation -= 360;

            // Check if rotation is close to default orientations (0°, 90°, 180°, 270°)
            // Allow small tolerance for floating point precision
            float tolerance = 5f;

            bool isDefault = (yRotation <= tolerance || yRotation >= (360 - tolerance)) || // 0°
                           (Math.Abs(yRotation - 90) <= tolerance) ||                      // 90°
                           (Math.Abs(yRotation - 180) <= tolerance) ||                     // 180°
                           (Math.Abs(yRotation - 270) <= tolerance);                       // 270°

            if (config.debug)
                Puts($"[Roam Debug] Barricade rotation check: {yRotation:F1}° - IsDefault: {isDefault}");

            return !isDefault; // Return true if NOT in default orientation
        }

        // Helper method to check if players are in the wall spawn area
        private bool IsPlayerInSpawnArea(Vector3 position, float checkRadius = 3f)
        {
            // Check for players in a larger area around the wall spawn position

            var colliders = Physics.OverlapSphere(position, checkRadius, LayerMask.GetMask("Player (Server)"));

            foreach (var collider in colliders)
            {
                var player = collider.GetComponentInParent<BasePlayer>();
                if (player != null && player.IsConnected && !player.IsDead())
                {
                    if (config.debug)
                        Puts($"[Roam Debug] Player {player.displayName} found at distance {Vector3.Distance(position, player.transform.position):F1}m from wall spawn");
                    return true;
                }
            }

            // Also check for players using a simple distance check as backup
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player != null && player.IsConnected && !player.IsDead())
                {
                    float distance = Vector3.Distance(position, player.transform.position);
                    if (distance <= checkRadius)
                    {
                        if (config.debug)
                            Puts($"[Roam Debug] Player {player.displayName} found at distance {distance:F1}m from wall spawn (backup check)");
                        return true;
                    }
                }
            }

            return false;
        }

        // Helper method to check if there are walls at the spawn position
        private bool IsWallAtPosition(Vector3 position, float radius = 2f)
        {
            var colliders = Physics.OverlapSphere(position, radius);

            foreach (var collider in colliders)
            {
                var entity = collider.GetComponentInParent<BaseEntity>();
                if (entity != null && !entity.IsDestroyed)
                {
                    // Check if it's a wall or building block
                    if (entity.PrefabName.Contains("wall") || entity is BuildingBlock)
                    {
                        if (config.debug)
                            Puts($"[Roam Debug] Wall/building detected at spawn position: {entity.ShortPrefabName}");
                        return true;
                    }
                }
            }

            return false;
        }
        #endregion

        #region Components

        public class PlayerStats
        {
            public int kills = 0;
            public int deaths = 0;
            public int headshots = 0;
            public int damage = 0;
        }
        public class TeamStats
        {
            public List<string> members = new List<string>();
            public int kills = 0;
            public int deaths = 0;
            public int damage = 0;
            public int headshots = 0;
            public int akKills = 0;
            public int m2Kills = 0;
        }

        public class TeamGroup
        {
            public string Team { get; set; }
            public int TotalKills { get; set; }
            public int TotalDeaths { get; set; }
            public int TotalDamage { get; set; }
            public int TotalHeadshots { get; set; }
            public int AKKills { get; set; }
            public int M2Kills { get; set; }
            public List<PlayerData> Players { get; set; } = new List<PlayerData>();
        }

        public class PlayerData
        {
            public string uid { get; set; }
            public int kills { get; set; }
            public int deaths { get; set; }
            public int headshots { get; set; }
            public int damage { get; set; }
            public float kdr { get; set; }
            public string name { get; set; }
        }

        public class AutomaticRoamBubbleComp : MonoBehaviour
        {
            public SphereCollider innerCollider;
            public List<SphereEntity> innerSpheres = new List<SphereEntity>();
            public MapMarkerGenericRadius roamMarker = null;
            public VendingMachineMapMarker vendingMarker = null;
            public Dictionary<string, TeamStats> teamStats = new Dictionary<string, TeamStats>();
            public Dictionary<string, PlayerStats> playerStats = new Dictionary<string, PlayerStats>();
            public Dictionary<string, WeaponCount> weaponCounts = new Dictionary<string, WeaponCount>();
            public string biome = "Grass";
            public float roamTime = 0f;
            public float roamEndTime;
            public float initialRoamTime;
            private float lastTimerUpdate = 0f;
            public Timer decayTimer;
            public bool hasEnded = false; // Flag to prevent multiple end calls

            public class WeaponCount
            {
                public int akCount = 0;
                public int m2Count = 0;
            }

            void Awake()
            {
                gameObject.layer = (int)Layer.Reserved1;
                gameObject.name = "Roam Bubble";
                enabled = false;
            }

            void Update()
            {
                if (roamTime > 0)
                {
                    roamTime -= UnityEngine.Time.deltaTime;

                    // Update timer UI only every minute (60 seconds) instead of every frame
                    if (UnityEngine.Time.time - lastTimerUpdate >= 60f)
                    {
                        lastTimerUpdate = UnityEngine.Time.time;

                        foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
                        {
                            BasePlayer player = BasePlayer.Find(kvp.Key);
                            if (player != null && player.IsConnected)
                            {
                                Instance.UpdateTimerUI(player, roamTime);
                            }
                        }
                    }
                }
                else if (!hasEnded) // Only run end logic once
                {
                    hasEnded = true; // Set flag to prevent multiple executions

                    // Enhanced roam ending validation and cleanup
                    if (config.debug)
                        Instance.Puts($"[Roam Debug] Roam ending - performing cleanup and validation");

                    // Immediate cleanup to prevent issues
                    try
                    {
                        // Ensure all players are properly removed from the roam
                        Instance.CleanupPlayersFromRoam(this);

                        // Force end any lingering processes
                        Instance.ForceEndRoamProcesses(this);

                    KeyValuePair<string, TeamStats> winningTeam = teamStats.Count > 0
                        ? teamStats.Aggregate((x, y) => x.Value.kills > y.Value.kills ? x : y)
                        : default(KeyValuePair<string, TeamStats>);

                    bool isNull = winningTeam.Equals(default(KeyValuePair<string, TeamStats>)) || winningTeam.Value.kills == 0;

                    if (isNull)
                    {
                        if (config.messages.sendEndResultToChat)
                            Instance.SendGlobalMessage(Instance.lang.GetMessage("RoamEndedNoWinners", Instance));

                        if (config.messages.sendEndResultToDiscord)
                        {
                            Instance.SendRoamResults(this); // Call SendRoamResults on the current instance (this)
                        }
                    }
                    else
                    {
                        StringBuilder members = new StringBuilder();
                        string topKiller = "";
                        int maxKills = 0;
                        foreach (string pid in winningTeam.Value.members)
                        {
                            BasePlayer ply = BasePlayer.FindAwakeOrSleeping(pid);
                            if (ply == null) members.Append(pid + " - 0 kills - 0 deaths\n");
                            else
                            {
                                int playerKills = teamStats[winningTeam.Key].kills / teamStats[winningTeam.Key].members.Count;
                                int playerDeaths = teamStats[winningTeam.Key].deaths / teamStats[winningTeam.Key].members.Count;
                                members.Append($"{ply.displayName} - {playerKills} kills - {playerDeaths} deaths\n");

                                if (playerKills > maxKills)
                                {
                                    maxKills = playerKills;
                                    topKiller = ply.displayName;
                                }
                            }
                        }

                        int totalKills = 0, totalAKKills = 0, totalM2Kills = 0, totalDeaths = 0, totalDamage = 0, totalHeadshots = 0;
                        int totalPlayers = 0, totalTeams = 0, totalAKs = 0, totalM2s = 0;
                        foreach (var stats in teamStats.Values)
                        {
                            totalKills += stats.kills;
                            totalAKKills += stats.akKills;
                            totalM2Kills += stats.m2Kills;
                            totalDeaths += stats.deaths;
                            totalDamage += stats.damage;
                            totalHeadshots += stats.headshots;
                            totalPlayers += stats.members.Count;
                        }
                        // Count only actual clan teams (not individual players)
                        totalTeams = Instance.CountActualTeams(teamStats);

                        // Calculate total AKs and M2s in the roam
                        foreach (var weaponCount in weaponCounts.Values)
                        {
                            totalAKs += weaponCount.akCount;
                            totalM2s += weaponCount.m2Count;
                        }

                        if (config.messages.sendEndResultToChat)
                            Instance.SendInGameRoamResults(this); // Pass 'this' (AutomaticRoamBubbleComp) to match the method signature

                        if (config.messages.sendEndResultToDiscord)
                        {
                            Instance.SendRoamResults(this); // Call SendRoamResults on the current instance (this)
                        }
                    }

                    // Start high wall decay process before destroying the roam
                    Instance.StartHighWallDecayAfterRoam();

                        // Enhanced cleanup before destroying the roam
                        Instance.PerformFinalCleanup(this);

                        // Final destruction with multiple attempts
                        Instance.DestroyRoamSafely(this);
                    }
                    catch (System.Exception ex)
                    {
                        Instance.Puts($"[Roam Error] Exception during roam ending: {ex.Message}");
                        // Force cleanup even if there's an error
                        try
                        {
                            DeleteCircle();
                            Instance.activeRoams.Remove(this);
                            DestroyImmediate(gameObject);
                        }
                        catch (System.Exception ex2)
                        {
                            Instance.Puts($"[Roam Error] Critical error during force cleanup: {ex2.Message}");
                        }
                    }
                }
            }

            void OnDestroy()
            {
                DeleteCircle();
                Instance.activeRoams.Remove(this);
                CancelInvoke(nameof(UpdatePlayerUI)); // Clean up the invoke when the roam ends
                CancelInvoke(nameof(CheckForHelicoptersInBubble)); // Clean up helicopter monitoring
                CancelInvoke(nameof(ContinuousWeaponCheck)); // Clean up weapon monitoring
            }

            void OnTriggerEnter(Collider col)
            {
                BasePlayer player = col?.GetComponentInParent<BasePlayer>();
                if (player == null) return;

                // Exclude vanished admins from roam participation
                if (Instance.IsPlayerVanished(player)) return;

                // Check if player is mounted on a helicopter and prevent entry
                if (Instance.IsPlayerOnHelicopter(player))
                {
                    Instance.EjectPlayerFromHelicopter(player);
                    player.ChatMessage("❌ You cannot enter the roam bubble while flying a helicopter! You have been ejected.");
                    return;
                }

                // Check if player is already in bubble to avoid duplicate key error
                if (!Instance.playersInBubble.ContainsKey(player.UserIDString))
                {
                    Instance.playersInBubble.Add(player.UserIDString, this);
                }

                // Initialize player stats and weapon counts when entering the bubble
                if (!playerStats.ContainsKey(player.UserIDString))
                {
                    playerStats[player.UserIDString] = new PlayerStats();
                }
                if (!weaponCounts.ContainsKey(player.UserIDString))
                {
                    weaponCounts[player.UserIDString] = new WeaponCount();
                    CountPlayerWeapons(player); // Count AKs and M2s for this player
                }

                if (config.ui.useUI)
                {
                    Instance.CreateRoamUI(player);
                }

                EnforceRestrictions(player);

                // Start updating the UI every 5 seconds for this player
                InvokeRepeating(nameof(UpdatePlayerUI), 0f, 5f); // Start immediately and repeat every 5 seconds

                // Also start updating weapon counts periodically (like maze plugin)
                InvokeRepeating(nameof(UpdateWeaponCounts), 10f, 30f); // Update weapon counts every 30 seconds

                // Start continuous weapon restriction monitoring
                InvokeRepeating(nameof(ContinuousWeaponCheck), 1f, 2f); // Check every 2 seconds

                // Note: Accelerated decay will be started in CreateBubble method to avoid duplicate timers

                // Start helicopter monitoring
                InvokeRepeating(nameof(CheckForHelicoptersInBubble), 5f, 5f); // Check every 5 seconds
            }

            void OnTriggerExit(Collider col)
            {
                BasePlayer player = col?.GetComponentInParent<BasePlayer>();
                if (player == null || !Instance.playersInBubble.ContainsKey(player.UserIDString)) return;

                // Always remove from bubble regardless of vanish status (in case they vanished while inside)
                Instance.playersInBubble.Remove(player.UserIDString);

                if (config.ui.useUI)
                {
                    CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                }

                // Stop updating the UI for this player when they leave
                CancelInvoke(nameof(UpdatePlayerUI));

                // Stop helicopter monitoring when last player leaves
                if (Instance.playersInBubble.Count == 0)
                {
                    CancelInvoke(nameof(CheckForHelicoptersInBubble));
                    CancelInvoke(nameof(ContinuousWeaponCheck));
                }
            }

            private void UpdatePlayerUI()
            {
                foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
                {
                    BasePlayer player = BasePlayer.Find(kvp.Key);
                    if (player != null && player.IsConnected && config.ui.useUI && !Instance.IsPlayerVanished(player))
                    {
                        Instance.CreateRoamUI(player, roamTime); // Recreate the UI with updated stats
                    }
                }
            }

            private void UpdateWeaponCounts()
            {
                // Update weapon counts for all players in this roam bubble (like maze plugin)
                foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
                {
                    BasePlayer player = BasePlayer.Find(kvp.Key);
                    if (player != null && player.IsConnected)
                    {
                        CountPlayerWeapons(player);
                    }
                }
            }

            private void CheckForHelicoptersInBubble()
            {
                // Check all players in this bubble for helicopter usage
                foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
                {
                    BasePlayer player = BasePlayer.Find(kvp.Key);
                    if (player != null && player.IsConnected && !Instance.IsPlayerVanished(player))
                    {
                        if (Instance.IsPlayerOnHelicopter(player))
                        {
                            Instance.EjectPlayerFromHelicopter(player);
                            player.ChatMessage("❌ Helicopters are not allowed in the roam bubble! You have been ejected.");

                            if (config.debug)
                            {
                                Instance.Puts($"[Roam Debug] Ejected {player.displayName} from helicopter inside bubble");
                            }
                        }
                    }
                }
            }

            public void StartAcceleratedDecay()
            {
                if (!config.decaySettings.enableAcceleratedDecay)
                {
                    if (config.debug)
                        Instance.Puts("[Roam Decay] Accelerated decay is disabled in config");
                    return;
                }

                // Stop any existing decay timer to prevent duplicates
                if (decayTimer != null && !decayTimer.Destroyed)
                {
                    decayTimer.Destroy();
                    decayTimer = null;
                }

                if (config.debug)
                    Instance.Puts($"[Roam Decay] Starting accelerated decay timer - Interval: {config.decaySettings.decayCheckInterval}s, Multiplier: {config.decaySettings.decaySpeedMultiplier}x");

                decayTimer = Instance.timer.Repeat(config.decaySettings.decayCheckInterval, 0, () =>
                {
                    if (this == null || gameObject == null || innerCollider == null)
                    {
                        if (config.debug)
                            Instance.Puts("[Roam Decay] Decay timer stopped - roam component destroyed");
                        decayTimer?.Destroy();
                        decayTimer = null;
                        return;
                    }

                    ProcessDecayForBubble();
                });
            }

            public void ProcessDecayForBubble()
            {
                try
                {
                    if (config.debug)
                        Instance.Puts($"[Roam Decay] Starting decay process for bubble at {transform.position}");

                    // Find all structures within the bubble radius using multiple methods for better coverage
                    var structures = new HashSet<BaseEntity>();

                    // Method 1: Physics.OverlapSphere with expanded layer masks
                    var colliders = Physics.OverlapSphere(transform.position, innerCollider.radius,
                        LayerMask.GetMask("Construction", "Deployed", "Default", "World", "Terrain"));

                    if (config.debug)
                        Instance.Puts($"[Roam Decay] Found {colliders.Length} colliders in bubble radius {innerCollider.radius}m at position {transform.position}");

                    foreach (var collider in colliders)
                    {
                        var entity = collider.GetComponentInParent<BaseEntity>();
                        if (entity != null && ShouldAccelerateDecay(entity))
                        {
                            structures.Add(entity);
                            if (config.debug)
                                Instance.Puts($"[Roam Decay] Added entity from collider: {entity.ShortPrefabName} (Type: {entity.GetType().Name})");
                        }
                    }

                    // Method 2: Check all entities in range (more comprehensive)
                    var allEntities = BaseEntity.saveList.Where(e => e != null && !e.IsDestroyed &&
                        Vector3.Distance(e.transform.position, transform.position) <= innerCollider.radius);

                    foreach (var entity in allEntities)
                    {
                        if (ShouldAccelerateDecay(entity))
                        {
                            structures.Add(entity);
                        }
                    }

                    // PRIORITY: Check specifically for converted high walls first
                    if (config.debug)
                        Instance.Puts($"[Roam Decay] Checking {Instance.convertedHighWalls.Count} tracked converted high walls");

                    foreach (var wallId in Instance.convertedHighWalls.ToList())
                    {
                        var wall = BaseNetworkable.serverEntities.Find(new NetworkableId(wallId)) as BaseEntity;
                        if (wall != null && !wall.IsDestroyed)
                        {
                            float distance = Vector3.Distance(wall.transform.position, transform.position);
                            if (config.debug)
                                Instance.Puts($"[Roam Decay] Converted wall found - Distance: {distance:F1}m, Bubble radius: {innerCollider.radius}m, Prefab: {wall.ShortPrefabName}");

                            // Always add converted walls regardless of distance (they should decay)
                            structures.Add(wall);
                            if (config.debug)
                                Instance.Puts($"[Roam Decay] FORCE ADDED converted high wall to decay list - Health: {(wall as BaseCombatEntity)?.Health():F1}/{(wall as BaseCombatEntity)?.MaxHealth():F1}");
                        }
                        else
                        {
                            // Remove destroyed walls from tracking
                            Instance.convertedHighWalls.Remove(wallId);
                            if (config.debug)
                                Instance.Puts($"[Roam Decay] Removed destroyed wall from tracking");
                        }
                    }

                    if (config.debug)
                        Instance.Puts($"[Roam Decay] Found {structures.Count} structures eligible for decay");

                    // Apply accelerated decay to found structures
                    foreach (var structure in structures)
                    {
                        ApplyAcceleratedDecay(structure);
                    }
                }
                catch (System.Exception ex)
                {
                    Instance.PrintError($"[Roam Decay] Error in ProcessDecayForBubble: {ex.Message}");
                }
            }

            public bool ShouldAccelerateDecay(BaseEntity entity)
            {
                if (entity == null || entity.IsDestroyed) return false;

                // Skip entities that can't take damage
                if (!(entity is BaseCombatEntity)) return false;

                string entityType = entity.GetType().Name;
                string prefabName = entity.ShortPrefabName;
                string fullPrefabName = entity.PrefabName;

                // Skip certain entities that shouldn't decay
                if (IsExcludedFromDecay(entity, fullPrefabName))
                {
                    return false;
                }

                // Prioritize converted high walls - they should always decay
                if (Instance.convertedHighWalls.Contains(entity.net.ID.Value))
                {
                    if (config.debug)
                        Instance.Puts($"[Roam Decay] {entityType} ({prefabName}) marked for decay - Converted high wall");
                    return true;
                }

                bool shouldDecay = false;

                // Check if it's a building block
                if (entity is BuildingBlock)
                {
                    if (config.decaySettings.applyToAllStructures)
                    {
                        shouldDecay = true;
                        if (config.debug)
                            Instance.Puts($"[Roam Decay] {entityType} ({prefabName}) marked for decay - Building block");
                    }
                }
                // Check if it's a deployable
                else if (entity is Deployable)
                {
                    if (config.decaySettings.applyToAllStructures)
                    {
                        shouldDecay = true;
                        if (config.debug)
                            Instance.Puts($"[Roam Decay] {entityType} ({prefabName}) marked for decay - Deployable");
                    }
                    // Special case for barricades only mode
                    else if (config.decaySettings.applyToBarricadesOnly && IsBarricade(fullPrefabName))
                    {
                        shouldDecay = true;
                        if (config.debug)
                            Instance.Puts($"[Roam Decay] {entityType} ({prefabName}) marked for decay - Barricade only mode");
                    }
                }
                // Check for other structure types
                else if (IsStructureType(entity))
                {
                    if (config.decaySettings.applyToAllStructures)
                    {
                        shouldDecay = true;
                        if (config.debug)
                            Instance.Puts($"[Roam Decay] {entityType} ({prefabName}) marked for decay - Structure type");
                    }
                }

                return shouldDecay;
            }

            private bool IsExcludedFromDecay(BaseEntity entity, string prefabName)
            {
                // Exclude players, NPCs, animals
                if (entity is BasePlayer || entity is BaseNpc || entity is BaseAnimalNPC)
                {
                    return true;
                }

                // Exclude all vehicles and vehicle modules
                if (entity is BaseVehicle || entity is BaseHelicopter || entity is BaseBoat ||
                    entity is HotAirBalloon || entity is CH47Helicopter)
                {
                    return true;
                }

                // Exclude resource nodes, trees, collectibles
                if (entity is ResourceEntity || entity is TreeEntity || entity is OreResourceEntity ||
                    entity is CollectibleEntity || entity is GrowableEntity)
                {
                    return true;
                }

                // Exclude NPCs and AI entities
                if (entity is BradleyAPC || entity is BaseHelicopter || entity is SamSite)
                {
                    return true;
                }

                // Exclude certain prefabs that shouldn't decay
                if (prefabName.Contains("sphere") || prefabName.Contains("marker") ||
                    prefabName.Contains("trigger") || prefabName.Contains("invisible") ||
                    prefabName.Contains("patrol") || prefabName.Contains("bradley") ||
                    prefabName.Contains("ch47") || prefabName.Contains("horse"))
                {
                    return true;
                }

                // Exclude specific vehicle types by prefab name
                if (prefabName.Contains("minicopter") || prefabName.Contains("scrapheli") ||
                    prefabName.Contains("rowboat") || prefabName.Contains("rhib") ||
                    prefabName.Contains("kayak") || prefabName.Contains("submarine") ||
                    prefabName.Contains("modularcar") || prefabName.Contains("bike") ||
                    prefabName.Contains("tugboat") || prefabName.Contains("sled"))
                {
                    return true;
                }

                return false;
            }

            private bool IsBarricade(string prefabName)
            {
                return prefabName.Contains("barricade") || prefabName.Contains("cover") ||
                       prefabName.Contains("wall.external.high.wood"); // Include converted high walls
            }

            private bool IsStructureType(BaseEntity entity)
            {
                return entity is Door || entity is StorageContainer || entity is BaseOven ||
                       entity is Workbench || entity is RepairBench || entity is ResearchTable ||
                       entity is AutoTurret || entity is FlameTurret || entity is GunTrap ||
                       entity is Landmine || entity is BearTrap || entity is SleepingBag ||
                       entity is Signage || entity is Locker || entity is VendingMachine ||
                       entity is ElectricBattery || entity is ElectricSwitch || entity is WireTool ||
                       entity is Planner || entity is BaseLadder || entity is StabilityEntity;
            }

            public void ApplyAcceleratedDecay(BaseEntity entity)
            {
                if (entity == null || entity.IsDestroyed)
                {
                    if (config.debug)
                        Instance.Puts($"[Roam Decay] Skipping null or destroyed entity");
                    return;
                }

                var combatEntity = entity as BaseCombatEntity;
                if (combatEntity == null)
                {
                    if (config.debug)
                        Instance.Puts($"[Roam Decay] Skipping non-combat entity: {entity.ShortPrefabName}");
                    return;
                }

                try
                {
                    // Calculate accelerated decay damage based on max health
                    float maxHealth = combatEntity.MaxHealth();
                    float currentHealth = combatEntity.Health();

                    if (config.debug)
                        Instance.Puts($"[Roam Decay] Processing {entity.ShortPrefabName} - Health: {currentHealth:F1}/{maxHealth:F1}");

                    // Skip if already at very low health to prevent spam
                    if (currentHealth <= 1.0f)
                    {
                        if (config.debug)
                            Instance.Puts($"[Roam Decay] Skipping {entity.ShortPrefabName} - health too low ({currentHealth:F1})");
                        return;
                    }

                    // Calculate decay damage as a percentage of max health for more effective decay
                    float decayPercentage = 0.05f * config.decaySettings.decaySpeedMultiplier; // 5% base decay per interval
                    float acceleratedDecayRate = maxHealth * decayPercentage;

                    // Ensure minimum decay damage but cap maximum to prevent instant destruction
                    acceleratedDecayRate = Mathf.Clamp(acceleratedDecayRate,
                        1.0f * config.decaySettings.decaySpeedMultiplier,
                        maxHealth * 0.25f); // Max 25% of health per tick

                    // Create proper hit info for decay damage
                    var hitInfo = new HitInfo();
                    hitInfo.damageTypes.Set(Rust.DamageType.Decay, acceleratedDecayRate);
                    hitInfo.DoHitEffects = false;
                    hitInfo.HitMaterial = 0;
                    hitInfo.PointStart = entity.transform.position;
                    hitInfo.PointEnd = entity.transform.position;
                    hitInfo.HitPositionWorld = entity.transform.position;

                    // Apply the damage
                    combatEntity.OnAttacked(hitInfo);

                    if (config.debug)
                    {
                        float newHealth = combatEntity.Health();
                        Instance.Puts($"[Roam Decay] Applied {acceleratedDecayRate:F1} decay damage to {entity.ShortPrefabName} (Health: {newHealth:F1}/{maxHealth:F1})");

                        // Log if entity was destroyed
                        if (newHealth <= 0 || combatEntity.IsDestroyed)
                        {
                            Instance.Puts($"[Roam Decay] {entity.ShortPrefabName} was destroyed by decay");
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    Instance.PrintError($"[Roam Decay] Error applying decay to {entity.ShortPrefabName}: {ex.Message}");
                }
            }

            public void CreateBubble(Vector3 position, float initialRadius, string biome, int time)
            {
                transform.position = position;
                transform.rotation = Quaternion.identity;
                roamTime = time;
                this.biome = biome;
                roamEndTime = UnityEngine.Time.realtimeSinceStartup + time; // Store end time
                initialRoamTime = time;
                lastTimerUpdate = UnityEngine.Time.time; // Initialize timer update tracking

                // Remove resource pickups (stone, sulfur, metal, driftwood, etc.) within the bubble radius when the roam starts
                Instance.RemoveResourcePickups(position, initialRadius);

                // Remove all existing buildings within the bubble radius when the roam starts
                Instance.RemoveAllBuildings(position, initialRadius);

                for (int i = 0; i < config.numOfSpheres; i++)
                {
                    var sphere = (SphereEntity)GameManager.server.CreateEntity("assets/prefabs/visualization/sphere.prefab", position, Quaternion.identity, true);
                    sphere.currentRadius = initialRadius * 2;
                    sphere.lerpSpeed = 0;
                    sphere.enableSaving = false;
                    sphere.Spawn();
                    innerSpheres.Add(sphere);
                }

                var innerRB = innerSpheres[0].gameObject.AddComponent<Rigidbody>();
                innerRB.useGravity = false;
                innerRB.isKinematic = true;

                innerCollider = gameObject.AddComponent<SphereCollider>();
                innerCollider.transform.position = innerSpheres[0].transform.position;
                innerCollider.isTrigger = true;
                innerCollider.radius = initialRadius;

                vendingMarker = GameManager.server.CreateEntity("assets/prefabs/deployable/vendingmachine/vending_mapmarker.prefab", position).GetComponent<VendingMachineMapMarker>();
                vendingMarker.enableSaving = false;
                vendingMarker.markerShopName = "Awaken Roams";
                vendingMarker.Spawn();
                vendingMarker.SendNetworkUpdate();

                roamMarker = GameManager.server.CreateEntity("assets/prefabs/tools/map/genericradiusmarker.prefab", position) as MapMarkerGenericRadius;
                if (roamMarker != null)
                {
                    roamMarker.alpha = 0.6f;
                    roamMarker.color1 = Color.black;
                    roamMarker.color2 = Color.black;
                    roamMarker.radius = initialRadius * 0.0062f;
                    roamMarker.Spawn();
                    roamMarker.SendUpdate();
                }

                gameObject.SetActive(true);
                enabled = true;
                Instance.activeRoams.Add(this);

                // Start accelerated decay if enabled
                if (config.decaySettings.enableAcceleratedDecay)
                {
                    StartAcceleratedDecay();
                }
            }

            public void DeleteCircle()
            {
                // Stop decay timer
                decayTimer?.Destroy();
                decayTimer = null;

                // Remove sphere entities with batched deletion to prevent FPS drops
                if (innerSpheres.Count > 0)
                {
                    Instance.StartBatchedSphereRemoval(innerSpheres.ToList());
                }
                innerSpheres.Clear();

                // Remove roam marker
                if (roamMarker != null && roamMarker.IsValid())
                {
                    roamMarker.Kill();
                }

                // Remove vending-style marker
                if (vendingMarker != null && vendingMarker.IsValid())
                {
                    vendingMarker.Kill();
                }

                // Clean up players and UI
                var players = Pool.GetList<KeyValuePair<string, AutomaticRoamBubbleComp>>();
                players.AddRange(Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList());

                foreach (var kvp in players)
                {
                    Instance.playersInBubble.Remove(kvp.Key);

                    BasePlayer player = BasePlayer.Find(kvp.Key);
                    if (player != null && player.IsConnected)
                    {
                        CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                    }
                }

                Pool.FreeList(ref players);
                Instance.activeRoams.Remove(this);
            }

            private void EnforceRestrictions(BasePlayer player)
            {
                List<Item> items = new List<Item>();
                if (player.inventory.containerMain != null)
                    items.AddRange(player.inventory.containerMain.itemList);
                if (player.inventory.containerBelt != null)
                    items.AddRange(player.inventory.containerBelt.itemList);
                if (player.inventory.containerWear != null)
                    items.AddRange(player.inventory.containerWear.itemList);

                // Also force unequip any currently held restricted weapon
                Item activeItem = player.GetActiveItem();
                if (activeItem?.info?.category == ItemCategory.Weapon && !Instance.IsWeaponAllowedByShortname(activeItem.info.shortname))
                {
                    player.svActiveItemID = new ItemId(0);
                    player.SendNetworkUpdate();
                }

                foreach (var item in items.ToList()) // Use ToList to avoid modification during iteration
                {
                    bool shouldRemove = false;
                    string restrictionMessage = "";

                    // Check weapons
                    if (item.info.category == ItemCategory.Weapon && !Instance.IsWeaponAllowedByShortname(item.info.shortname))
                    {
                        shouldRemove = true;
                        restrictionMessage = string.Format(Instance.lang.GetMessage("WeaponRestricted", Instance), item.info.shortname);
                    }
                    // Check attachments
                    else if (item.info.category == ItemCategory.Component && !Instance.IsAttachmentAllowed(item.info.shortname))
                    {
                        shouldRemove = true;
                        restrictionMessage = string.Format(Instance.lang.GetMessage("AttachmentRestricted", Instance), item.info.shortname);
                    }
                    // Check ammunition
                    else if (item.info.category == ItemCategory.Ammunition && !Instance.IsAmmunitionAllowed(item.info.shortname))
                    {
                        shouldRemove = true;
                        restrictionMessage = string.Format(Instance.lang.GetMessage("AmmunitionRestricted", Instance), item.info.shortname);
                    }
                    // Check explosives (tools, weapons, or misc items that are explosive)
                    else if (!Instance.IsExplosiveAllowed(item.info.shortname))
                    {
                        shouldRemove = true;
                        restrictionMessage = string.Format(Instance.lang.GetMessage("ExplosiveRestricted", Instance), item.info.shortname);
                    }

                    if (shouldRemove)
                    {
                        item.Remove();
                        player.ChatMessage(restrictionMessage);
                    }
                }
            }

            private void ContinuousWeaponCheck()
            {
                // Continuously check all players in this roam bubble for restricted weapons
                foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
                {
                    BasePlayer player = BasePlayer.Find(kvp.Key);
                    if (player != null && player.IsConnected && !Instance.IsPlayerVanished(player))
                    {
                        // Check currently held weapon
                        Item activeItem = player.GetActiveItem();
                        if (activeItem?.info?.category == ItemCategory.Weapon)
                        {
                            if (!Instance.IsWeaponAllowedByShortname(activeItem.info.shortname))
                            {
                                // Force unequip immediately
                                player.svActiveItemID = new ItemId(0);
                                player.SendNetworkUpdate();
                                player.ChatMessage($"❌ {activeItem.info.displayName.english} is not allowed in roam events!");
                            }
                            else if (!Instance.AreAttachmentsAllowed(activeItem.GetHeldEntity()))
                            {
                                // Force unequip due to restricted attachments
                                player.svActiveItemID = new ItemId(0);
                                player.SendNetworkUpdate();
                                player.ChatMessage("❌ Your weapon has restricted attachments!");
                            }
                        }
                    }
                }
            }

            public void CountPlayerWeapons(BasePlayer player)
            {
                if (player == null || string.IsNullOrEmpty(player.UserIDString))
                {
                    return;
                }

                // Ensure weaponCounts exists for the player; initialize if not present
                if (!weaponCounts.ContainsKey(player.UserIDString))
                {
                    weaponCounts[player.UserIDString] = new WeaponCount();
                }

                WeaponCount count = weaponCounts[player.UserIDString];

                // Reset counts before recounting (like maze plugin approach)
                count.akCount = 0;
                count.m2Count = 0;

                List<Item> items = new List<Item>();

                // Safely check and add items from each inventory container
                try
                {
                    if (player.inventory?.containerMain != null && player.inventory.containerMain.itemList != null)
                        items.AddRange(player.inventory.containerMain.itemList.Where(i => i != null));

                    if (player.inventory?.containerBelt != null && player.inventory.containerBelt.itemList != null)
                        items.AddRange(player.inventory.containerBelt.itemList.Where(i => i != null));

                    if (player.inventory?.containerWear != null && player.inventory.containerWear.itemList != null)
                        items.AddRange(player.inventory.containerWear.itemList.Where(i => i != null));
                }
                catch (Exception ex)
                {
                    return;
                }

                // Count AK-47s and M2s with enhanced tracking (like maze plugin)
                foreach (var item in items)
                {
                    if (item == null || item.info == null || string.IsNullOrEmpty(item.info.shortname))
                        continue;

                    string shortname = item.info.shortname.ToLower();

                    // Enhanced weapon detection - fixed shortnames
                    if (shortname == "rifle.ak" || shortname.Contains("rifle.ak"))
                    {
                        count.akCount += item.amount;

                        // Track individual item UIDs for better tracking (like maze plugin)
                        if (item.uid.IsValid)
                        {
                            // Store tracked weapon UIDs for potential future use
                            Instance.trackedWeaponItemUids.Add(item.uid);
                        }
                    }
                    else if (shortname == "lmg.m249" || shortname.Contains("lmg.m249") || shortname == "m249.entity" || shortname.Contains("m249.entity"))
                    {
                        count.m2Count += item.amount;

                        // Track individual item UIDs for better tracking (like maze plugin)
                        if (item.uid.IsValid)
                        {
                            // Store tracked weapon UIDs for potential future use
                            Instance.trackedWeaponItemUids.Add(item.uid);
                        }
                    }
                }
            }
        }
        #endregion

        #region Helper Functions
        public string GetTeamTag(BasePlayer player)
        {
            // Extract clan tag directly from display name
            string displayName = player.displayName;

            // Look for clan tag pattern like [123] at the start of display name
            if (displayName.StartsWith("[") && displayName.Contains("]"))
            {
                int endBracket = displayName.IndexOf(']');
                if (endBracket > 1) // Must have at least one character between brackets
                {
                    string extractedTag = displayName.Substring(1, endBracket - 1); // Extract content between first [ and ]

                    // Clean up any duplicate tags (like [123] [123] -> just 123)
                    if (extractedTag.Contains("] ["))
                    {
                        extractedTag = extractedTag.Split(new string[] { "] [" }, StringSplitOptions.None)[0];
                    }

                    return extractedTag; // Return just the clan tag (e.g., "123")
                }
            }

            // Fallback: Try Clans API if display name parsing fails
            if (Clans != null)
            {
                var clan = Clans.Call("GetClan", player.userID);
                if (clan != null)
                {
                    var clanName = Clans.Call("GetClanName", clan);
                    string name = clanName as string;
                    if (!string.IsNullOrEmpty(name))
                    {
                        return name;
                    }
                }

                var clanTag = Clans.Call("GetClanTag", player.userID);
                string tag = clanTag as string;
                if (!string.IsNullOrEmpty(tag))
                {
                    return tag;
                }
            }

            // Fall back to clean player name if not in a clan
            return GetCleanPlayerName(player);
        }

        private string GetCleanPlayerName(BasePlayer player)
        {
            // Get the original player name without any formatting
            // Use _displayName (internal field) or strip formatting from displayName
            string name = player._displayName ?? player.displayName;

            // Remove any clan tag formatting that might be in the display name
            if (name.Contains("[") && name.Contains("]"))
            {
                // Remove clan tag pattern like "[123] PlayerName" -> "PlayerName"
                int endBracket = name.IndexOf(']');
                if (endBracket >= 0 && endBracket < name.Length - 1)
                {
                    name = name.Substring(endBracket + 1).Trim();
                }
            }

            return name;
        }

        private string GetTeamDisplayName(string teamKey, List<string> members)
        {
            // If the teamKey looks like a clan identifier, use it
            if (IsActualClanTag(teamKey))
            {
                return teamKey; // Return clan tag or clan name
            }

            // If teamKey is a player name, try to get their clan identifier
            if (members.Count > 0 && Clans != null)
            {
                BasePlayer firstPlayer = BasePlayer.FindAwakeOrSleeping(members[0]);
                if (firstPlayer != null)
                {
                    // Try to get clan tag first
                    var clanTag = Clans.Call("GetClanTag", firstPlayer.userID) as string;
                    if (!string.IsNullOrEmpty(clanTag))
                    {
                        return clanTag;
                    }

                    // Try to get clan name as fallback
                    var clan = Clans.Call("GetClan", firstPlayer.userID);
                    if (clan != null)
                    {
                        var clanName = Clans.Call("GetClanName", clan) as string;
                        if (!string.IsNullOrEmpty(clanName))
                        {
                            return clanName;
                        }
                    }
                }
            }

            // Fall back to the original team key
            return teamKey;
        }

        private List<string> GetTeamMembers(BasePlayer player)
        {
            if (Clans == null) return new List<string> { player.UserIDString };

            // Get the clan object using the GetClan API
            var clan = Clans.Call("GetClan", player.userID);
            if (clan != null)
            {
                // Access the ClanMemebers property directly from the clan object
                var clanMembersProperty = clan.GetType().GetProperty("ClanMemebers");
                if (clanMembersProperty != null)
                {
                    var clanMembers = clanMembersProperty.GetValue(clan) as Dictionary<string, string>;
                    if (clanMembers != null && clanMembers.Count > 0)
                    {
                        return clanMembers.Keys.ToList();
                    }
                }
            }
            return new List<string> { player.UserIDString }; // Default to solo player if not in a clan
        }

        private void SendMessage(BasePlayer player, string key, params object[] args)
        {
            player.SendConsoleCommand("chat.add2", new object[] { 2, config.messages.avatarSteamID, string.Format(this.lang.GetMessage(key, this), args), config.messages.chatName, config.messages.chatNameColor, 1f });
        }

        private void SendGlobalMessage(string message)
        {
            ConsoleNetwork.BroadcastToAllClients("chat.add2", new object[] { 2, config.messages.avatarSteamID, message, config.messages.chatName, config.messages.chatNameColor, 1f });
        }

        private void SendBigGlobalMessage(string message)
        {
            // Send a larger, more prominent message with bigger chat name
            ConsoleNetwork.BroadcastToAllClients("chat.add2", new object[] { 0, config.messages.avatarSteamID, $"<size=20><color=#CCCCCC><b>{message}</b></color></size>", $"<size=18><b>{config.messages.chatName}</b></size>", config.messages.chatNameColor, 1f });
        }

        private void SendInGameRoamResults(AutomaticRoamBubbleComp roam)
        {
            // If no teams or no kills, send no winners message globally
            if (roam.teamStats.Count == 0 || roam.teamStats.Values.All(stats => stats.kills == 0))
            {
                SendGlobalMessage(Instance.lang.GetMessage("RoamEndedNoWinners", Instance));
                return;
            }

            // Get winning team (team with most kills)
            var winningTeam = roam.teamStats.OrderByDescending(t => t.Value.kills).FirstOrDefault();

            if (winningTeam.Key == null || winningTeam.Value.kills == 0)
            {
                SendGlobalMessage(Instance.lang.GetMessage("RoamEndedNoWinners", Instance));
                return;
            }

            // Calculate stats for the WINNING TEAM only (not all teams)
            int winningTeamKills = winningTeam.Value.kills;
            int winningTeamDeaths = winningTeam.Value.deaths;
            int winningTeamDamage = winningTeam.Value.damage;
            int winningTeamHeadshots = winningTeam.Value.headshots;
            int winningTeamAKs = 0, winningTeamM2s = 0;

            // Get weapon counts for winning team only
            if (roam.weaponCounts != null && roam.weaponCounts.ContainsKey(winningTeam.Key))
            {
                winningTeamAKs = roam.weaponCounts[winningTeam.Key].akCount;
                winningTeamM2s = roam.weaponCounts[winningTeam.Key].m2Count;
            }

            // Create the exact maze-style winner message format
            StringBuilder message = new StringBuilder();

            // Get proper team display name (clan tag/name, not player name)
            string teamDisplayName = GetTeamDisplayName(winningTeam.Key, winningTeam.Value.members);

            // Header line with exact format: "ROAM EVENT WINNERS - [clan tag]"
            message.Append($"<color=#CCCCCC><size=16><b>ROAM EVENT WINNERS - {teamDisplayName}</b></size></color>");

            // Stats line with exact format: "AK: X M249: X Kills: X Dmg: X,XXX" (winning team stats only)
            message.Append($"\n<color=#7000fd><b>AK: {winningTeamAKs} M249: {winningTeamM2s} Kills: {winningTeamKills} Dmg: {winningTeamDamage:N0}</b></color>");

            // Get top 3 players from winning team sorted by kills
            var topPlayers = winningTeam.Value.members
                .Select(pid =>
                {
                    BasePlayer ply = BasePlayer.FindAwakeOrSleeping(pid);
                    int kills = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].kills : 0;
                    return new { DisplayName = ply?.displayName ?? "Unknown", Kills = kills };
                })
                .OrderByDescending(p => p.Kills)
                .Take(3)
                .ToList();

            // Player lines with exact format: "[clan] playername - X kills" (smaller text for player names)
            foreach (var player in topPlayers)
            {
                if (player.Kills > 0) // Only show players with kills
                {
                    message.Append($"\n<color=#CCCCCC><size=12><b>[{teamDisplayName}] {player.DisplayName} - {player.Kills} kills</b></size></color>");
                }
            }

            SendBigGlobalMessage(message.ToString());

            // Award clan core points via API
            Instance.AwardClanCorePoints(winningTeam.Key, "roam");

            // Add roam win to AwakenStats for the winning clan
            Instance.AddRoamWinToStats(winningTeam.Key);
        }

        private class DiscordField
        {
            public string Name { get; set; }
            public string Value { get; set; }
            public bool Inline { get; set; }

            public DiscordField(string name, string value, bool inline = false)
            {
                Name = name;
                Value = value;
                Inline = inline;
            }
        }

        private void SendDiscordMessage(string webhook, string embedName, int embedColor, Dictionary<string, string> values, string content = null)
        {
            try
            {
                var embed = new
                {
                    title = embedName,
                    color = embedColor,
                    fields = values.Select(kv => new { name = kv.Key, value = kv.Value, inline = false }).ToArray()
                };

                var payload = new
                {
                    content = content,
                    embeds = new[] { embed }
                };

                string jsonPayload = JsonConvert.SerializeObject(payload);
                webrequest.Enqueue(webhook, jsonPayload, (code, response) =>
                {
                    if (code != 200)
                    {
                        PrintError($"Failed to send message to Discord. Status code: {code}");
                    }
                }, this, RequestMethod.POST, new Dictionary<string, string> { { "Content-Type", "application/json" } });
            }
            catch (Exception ex)
            {
                PrintError($"Error sending message to Discord: {ex.Message}");
            }
        }

        private void RemoveResourcePickups(Vector3 position, float radius)
        {
            Puts($"Starting optimized resource cleanup at position {position} with radius {radius}");

            // Start the optimized batched removal process
            StartBatchedEntityRemoval(position, radius);
        }

        private void RemoveAllBuildings(Vector3 position, float radius)
        {
            Puts($"Starting building removal at position {position} with radius {radius}");

            // Start the building removal process
            StartBatchedBuildingRemoval(position, radius);
        }

        private void StartBatchedEntityRemoval(Vector3 position, float radius)
        {
            try
            {
                // Use a layer mask to include natural resources and world objects, but avoid unnecessary layers
                var entities = Physics.OverlapSphere(position, radius, LayerMask.GetMask("Default", "Terrain", "World", "Resource"));

                if (entities.Length == 0)
                {
                    Puts("No entities found in cleanup area");
                    return;
                }

                // Filter entities to only those we want to remove
                var entitiesToRemove = new List<BaseEntity>();
                foreach (var entity in entities)
                {
                    BaseEntity baseEntity = entity.GetComponent<BaseEntity>();
                    if (baseEntity == null || baseEntity.IsDestroyed) continue;

                    string prefabName = baseEntity.PrefabName.ToLower();

                    // Targeted list of lag-causing resource pickups, driftwood, cacti, plants, and berries (excluding trees)
                    if (prefabName.Contains("pickup.stone") ||
                        prefabName.Contains("pickup.metal.ore") ||
                        prefabName.Contains("pickup.sulfur.ore") ||
                        prefabName.Contains("pickup.wood") || // Driftwood and other wood pickups
                        prefabName.Contains("collectable") || // Generic collectables (e.g., mushrooms, plants)
                        prefabName.Contains("ore") || // Additional ore types
                        prefabName.Contains("resource") || // General resource entities
                        prefabName.Contains("driftwood") || // Specifically target driftwood
                        prefabName.Contains("cactus") || // Target cacti (e.g., small or large cacti)
                        prefabName.Contains("plant.") || // Target plants (e.g., hemp, corn, pumpkin)
                        prefabName.Contains("berry") || // Target berry bushes
                        prefabName.Contains("bush.")) // Target bushes
                    {
                        entitiesToRemove.Add(baseEntity);
                    }
                }

                if (entitiesToRemove.Count == 0)
                {
                    Puts("No lag-causing entities found to remove");
                    return;
                }

                Puts($"Found {entitiesToRemove.Count} entities to remove - starting batched removal");

                // Start the batched removal process
                ProcessEntityRemovalBatch(entitiesToRemove, 0);
            }
            catch (Exception ex)
            {
                Puts($"Error in StartBatchedEntityRemoval: {ex.Message}");
            }
        }

        private void StartBatchedBuildingRemoval(Vector3 position, float radius)
        {
            try
            {
                // Use a broader layer mask to catch all building entities
                var entities = Physics.OverlapSphere(position, radius, LayerMask.GetMask("Default", "Construction", "Deployed"));

                if (entities.Length == 0)
                {
                    Puts("No entities found in building cleanup area");
                    return;
                }

                // Filter entities to only buildings and deployables
                var buildingsToRemove = new List<BaseEntity>();
                foreach (var entity in entities)
                {
                    BaseEntity baseEntity = entity.GetComponent<BaseEntity>();
                    if (baseEntity == null || baseEntity.IsDestroyed) continue;

                    // Check if it's a building block, deployable, or structure
                    if (baseEntity is BuildingBlock ||
                        baseEntity is Deployable ||
                        baseEntity is Door ||
                        baseEntity is StorageContainer ||
                        baseEntity is BaseOven ||
                        baseEntity is Workbench ||
                        baseEntity is RepairBench ||
                        baseEntity is ResearchTable ||
                        baseEntity is AutoTurret ||
                        baseEntity is FlameTurret ||
                        baseEntity is GunTrap ||
                        baseEntity is Landmine ||
                        baseEntity is BearTrap ||
                        baseEntity is SleepingBag ||
                        baseEntity is Signage ||
                        baseEntity is Locker ||
                        baseEntity is VendingMachine)
                    {
                        buildingsToRemove.Add(baseEntity);
                    }
                }

                if (buildingsToRemove.Count == 0)
                {
                    Puts("No buildings found to remove");
                    return;
                }

                Puts($"Found {buildingsToRemove.Count} buildings to remove - starting batched removal");

                // Start the batched building removal process
                ProcessBuildingRemovalBatch(buildingsToRemove, 0);
            }
            catch (Exception ex)
            {
                Puts($"Error in StartBatchedBuildingRemoval: {ex.Message}");
            }
        }

        private void ProcessEntityRemovalBatch(List<BaseEntity> entitiesToRemove, int currentIndex)
        {
            try
            {
                int batchSize = 5; // Remove only 5 entities per frame to prevent FPS drops
                int processed = 0;
                int removed = 0;

                // Process a small batch of entities
                for (int i = currentIndex; i < entitiesToRemove.Count && processed < batchSize; i++)
                {
                    var entity = entitiesToRemove[i];
                    if (entity != null && !entity.IsDestroyed)
                    {
                        try
                        {
                            string prefabName = entity.PrefabName;
                            entity.Kill();
                            removed++;
                            // Puts($"Removed lag-causing entity: {prefabName}"); // Disabled to reduce console spam
                        }
                        catch (Exception ex)
                        {
                            Puts($"Failed to remove entity {entity.PrefabName}: {ex.Message}");
                        }
                    }
                    processed++;
                }

                int nextIndex = currentIndex + processed;

                // If there are more entities to process, schedule the next batch
                if (nextIndex < entitiesToRemove.Count)
                {
                    // Schedule next batch in 0.1 seconds to spread the load
                    timer.Once(0.1f, () => ProcessEntityRemovalBatch(entitiesToRemove, nextIndex));

                    if (removed > 0)
                    {
                        Puts($"Batch complete: removed {removed} entities. Progress: {nextIndex}/{entitiesToRemove.Count}");
                    }
                }
                else
                {
                    // All entities processed
                    Puts($"Entity cleanup complete! Total entities removed: {removed}");
                }
            }
            catch (Exception ex)
            {
                Puts($"Error in ProcessEntityRemovalBatch: {ex.Message}");
            }
        }

        private void ProcessBuildingRemovalBatch(List<BaseEntity> buildingsToRemove, int currentIndex)
        {
            try
            {
                int batchSize = 3; // Remove only 3 buildings per frame to prevent FPS drops (buildings are larger)
                int processed = 0;
                int removed = 0;

                // Process a small batch of buildings
                for (int i = currentIndex; i < buildingsToRemove.Count && processed < batchSize; i++)
                {
                    var building = buildingsToRemove[i];
                    if (building != null && !building.IsDestroyed)
                    {
                        try
                        {
                            string prefabName = building.PrefabName;
                            building.Kill();
                            removed++;
                            if (config.debug)
                            {
                                Puts($"Removed building: {prefabName}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Puts($"Failed to remove building {building.PrefabName}: {ex.Message}");
                        }
                    }
                    processed++;
                }

                int nextIndex = currentIndex + processed;

                // If there are more buildings to process, schedule the next batch
                if (nextIndex < buildingsToRemove.Count)
                {
                    // Schedule next batch in 0.15 seconds to spread the load (slower for buildings)
                    timer.Once(0.15f, () => ProcessBuildingRemovalBatch(buildingsToRemove, nextIndex));

                    if (removed > 0)
                    {
                        Puts($"Building removal batch complete: removed {removed} buildings. Progress: {nextIndex}/{buildingsToRemove.Count}");
                    }
                }
                else
                {
                    // All buildings processed
                    Puts($"Building cleanup complete! Total buildings removed: {removed}");
                }
            }
            catch (Exception ex)
            {
                Puts($"Error in ProcessBuildingRemovalBatch: {ex.Message}");
            }
        }

        private void StartBatchedSphereRemoval(List<SphereEntity> spheresToRemove)
        {
            if (spheresToRemove.Count == 0) return;

            Puts($"Starting batched removal of {spheresToRemove.Count} sphere entities");
            ProcessSphereRemovalBatch(spheresToRemove, 0);
        }

        private void ProcessSphereRemovalBatch(List<SphereEntity> spheresToRemove, int currentIndex)
        {
            try
            {
                int batchSize = 3; // Remove only 3 spheres per frame (spheres are larger objects)
                int processed = 0;
                int removed = 0;

                // Process a small batch of spheres
                for (int i = currentIndex; i < spheresToRemove.Count && processed < batchSize; i++)
                {
                    var sphere = spheresToRemove[i];
                    if (sphere != null && sphere.IsValid())
                    {
                        try
                        {
                            sphere.Kill();
                            removed++;
                        }
                        catch (Exception ex)
                        {
                            Puts($"Failed to remove sphere entity: {ex.Message}");
                        }
                    }
                    processed++;
                }

                int nextIndex = currentIndex + processed;

                // If there are more spheres to process, schedule the next batch
                if (nextIndex < spheresToRemove.Count)
                {
                    // Schedule next batch in 0.05 seconds (faster for spheres since there are fewer)
                    timer.Once(0.05f, () => ProcessSphereRemovalBatch(spheresToRemove, nextIndex));

                    if (removed > 0)
                    {
                        Puts($"Sphere batch complete: removed {removed} spheres. Progress: {nextIndex}/{spheresToRemove.Count}");
                    }
                }
                else
                {
                    // All spheres processed
                    Puts($"Sphere cleanup complete! Total spheres removed: {removed}");
                }
            }
            catch (Exception ex)
            {
                Puts($"Error in ProcessSphereRemovalBatch: {ex.Message}");
            }
        }

        private void SendRoamResults(AutomaticRoamBubbleComp roam)
        {
            if (string.IsNullOrEmpty(config.webhook))
            {
                PrintError("Discord webhook URL is not configured.");
                return;
            }

            // Calculate team statistics similar to maze plugin
            var teamGroups = roam.teamStats
                .Select(kvp => new TeamGroup {
                    Team = kvp.Key,
                    TotalKills = kvp.Value.kills,
                    TotalDeaths = kvp.Value.deaths,
                    TotalDamage = kvp.Value.damage,
                    TotalHeadshots = kvp.Value.headshots,
                    AKKills = kvp.Value.akKills,
                    M2Kills = kvp.Value.m2Kills,
                    Players = kvp.Value.members.Select(pid => {
                        BasePlayer ply = BasePlayer.FindAwakeOrSleeping(pid);
                        int kills = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].kills : 0;
                        int deaths = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].deaths : 0;
                        int headshots = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].headshots : 0;
                        int damage = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].damage : 0;
                        float kdr = deaths > 0 ? (float)kills / deaths : kills;
                        return new PlayerData { uid = pid, kills = kills, deaths = deaths, headshots = headshots, damage = damage, kdr = kdr, name = ply != null ? GetCleanPlayerName(ply) : "Unknown" };
                    }).OrderByDescending(p => p.kills).ToList()
                })
                .OrderByDescending(g => g.TotalKills)
                .ToList();

            // Get proper team display name for Discord embed
            var firstTeam = teamGroups.FirstOrDefault();
            string winningTeam = firstTeam != null ? GetTeamDisplayName(firstTeam.Team, firstTeam.Players.Select(p => p.uid).ToList()) : "No Team";

            // Build Winners section (top team only)
            var winGroup = teamGroups.FirstOrDefault();
            string winnersText;
            if (winGroup != null && winGroup.TotalKills > 0)
            {
                var allMembers = winGroup.Players
                    .Select(p => $"- [{p.name}](http://steamcommunity.com/profiles/{p.uid}) – {p.kills} Kills / {p.deaths} Deaths / {p.headshots} HS / {p.kdr:0.00} KDR");
                winnersText = $"- **[{winGroup.Team}]** (Total: {winGroup.TotalKills} Kills / {winGroup.TotalDeaths} Deaths / {winGroup.TotalHeadshots} HS)\n"
                            + string.Join("\n", allMembers);
            }
            else winnersText = "No winners data.";

            // Build Runner-ups section (other teams)
            var runnerUps = teamGroups
                .Where(g => g.Team != winningTeam)
                .Take(2)
                .Select((g, i) => $"- **[{g.Team}]** – {g.TotalKills} Kills / {g.TotalDeaths} Deaths");
            string runnerUpsText = runnerUps.Any()
                ? string.Join("\n", runnerUps)
                : "No runner-ups.";

            // Find top fragger across all teams
            var topFragger = teamGroups
                .SelectMany(g => g.Players)
                .OrderByDescending(p => p.kills)
                .ThenByDescending(p => p.kdr)
                .FirstOrDefault();
            string topFraggerText = topFragger != null
                ? $"- [{topFragger.name}](http://steamcommunity.com/profiles/{topFragger.uid}) – ``{topFragger.kills} Kills`` / ``{topFragger.deaths} Deaths``"
                : "No data.";

            // Calculate totals
            int totalKills = teamGroups.Sum(g => g.TotalKills);
            int totalDeaths = teamGroups.Sum(g => g.TotalDeaths);
            int totalDamage = teamGroups.Sum(g => g.TotalDamage);
            int totalHeadshots = teamGroups.Sum(g => g.TotalHeadshots);
            int totalPlayers = teamGroups.Sum(g => g.Players.Count);
            // Count only actual clan teams (not individual players)
            int totalTeams = Instance.CountActualTeamsFromGroups(teamGroups);
            int totalAKs = roam.weaponCounts.Values.Sum(w => w.akCount);
            int totalM2s = roam.weaponCounts.Values.Sum(w => w.m2Count);

            // Calculate duration
            TimeSpan duration = TimeSpan.FromSeconds(roam.initialRoamTime - roam.roamTime);

            // Build fields array dynamically (like maze plugin)
            var fieldsList = new List<object>();

            // Add statistics fields similar to maze plugin
            fieldsList.Add(new { name = "Details:", value = $"Total Kills: ``{totalKills:N0}``\nDuration: ``{duration.Minutes} min``", inline = true });
            fieldsList.Add(new { name = "‎", value = $"Total Players: ``{totalPlayers}``\nTotal Teams: ``{totalTeams}``", inline = true });
            fieldsList.Add(new { name = "Loot Check:", value = $"AKs: ``{totalAKs}``\nM2s: ``{totalM2s}``", inline = true });
            fieldsList.Add(new { name = "‎", value = $"{topFraggerText}", inline = false });
            fieldsList.Add(new { name = "Winners:", value = $"**[{winningTeam}]**", inline = true });
            fieldsList.Add(new { name = "Kills:", value = $"``{winGroup?.TotalKills ?? 0}``", inline = true });
            fieldsList.Add(new { name = "Damage / Headshots:", value = $"``{winGroup?.TotalDamage ?? 0}`` / ``{winGroup?.TotalHeadshots ?? 0}``", inline = true });

            // Add Members field with properly formatted member list
            if (winGroup != null && winGroup.Players.Any())
            {
                var membersList = winGroup.Players
                    .Select(p => $"- [{p.name}](http://steamcommunity.com/profiles/{p.uid}) – ``{p.kills} Kills`` / ``{p.deaths} Deaths``");
                string membersText = string.Join("\n", membersList);
                fieldsList.Add(new { name = "Members:", value = membersText, inline = false });
            }

            // Create Discord embed (using maze plugin style)
            var payload = new
            {
                content = "",
                embeds = new[]
                {
                    new
                    {
                        title = "Awaken Roams",
                        color = 16711680,
                        fields = fieldsList.ToArray(),
                        thumbnail = new {
                            url = config?.ui?.logoImageURL ?? "https://cdn.awakenrust.com/awaken_roams.png"
                        },
                        footer = new {
                            text = "Awaken Rust Servers",
                            icon_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1384783101514748007/MAIN.png"
                        }
                    }
                },
                username = "Awaken Events",
                avatar_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1384783101514748007/MAIN.png",
                attachments = new object[0]
            };

            string json = JsonConvert.SerializeObject(payload, Formatting.None);
            webrequest.EnqueuePost(config.webhook, json, (code, res) =>
            {
                if (code is not 200 and not 204)
                    PrintError($"Discord webhook error: {code} – {res}");
            }, this, RequestHeaders);
        }
        private static Dictionary<string, string> RequestHeaders => new Dictionary<string, string>
        {
            ["Content-Type"] = "application/json"
        };

        private bool IsWeaponAK(BaseEntity weapon)
        {
            if (weapon == null) return false;
            return weapon.ShortPrefabName.Contains("rifle.ak"); // Fixed AK-47 prefab name
        }

        private bool IsWeaponM2(BaseEntity weapon)
        {
            if (weapon == null) return false;
            // Check for both M249 shortnames
            return weapon.ShortPrefabName.Contains("lmg.m249") || weapon.ShortPrefabName.Contains("m249.entity");
        }

        private bool IsWeaponAllowed(BaseEntity weapon)
        {
            if (weapon == null) return true; // Allow null weapons (not held)
            string prefabName = weapon.ShortPrefabName;
            return IsWeaponAllowedByShortname(prefabName);
        }

        private bool IsWeaponAllowedByShortname(string shortname)
        {
            if (string.IsNullOrEmpty(shortname)) return true;

            // If using allowed weapons list, check if weapon is in the allowed list
            if (config.weaponRestrictions.useAllowedWeaponsList)
            {
                return config.weaponRestrictions.allowedWeapons.ContainsKey(shortname);
            }

            // Otherwise use the blocked weapons list approach
            if (config.weaponRestrictions.blockedWeapons.ContainsKey(shortname)) return false;
            return config.weaponRestrictions.allowOtherWeapons;
        }

        private bool IsAttachmentAllowed(string shortname)
        {
            if (string.IsNullOrEmpty(shortname)) return false;
            if (config.weaponRestrictions.blockedAttachments.ContainsKey(shortname)) return false;
            return config.weaponRestrictions.allowOtherAttachments;
        }

        private bool IsAmmunitionAllowed(string shortname)
        {
            if (string.IsNullOrEmpty(shortname)) return true;
            if (config.weaponRestrictions.blockedAmmunition.ContainsKey(shortname)) return false;
            return true;
        }

        private bool IsExplosiveAllowed(string shortname)
        {
            if (string.IsNullOrEmpty(shortname)) return true;
            if (config.weaponRestrictions.blockedExplosives.ContainsKey(shortname)) return false;
            return true;
        }

        private bool AreAttachmentsAllowed(BaseEntity weapon)
        {
            if (weapon == null) return true;
            BaseProjectile projectile = weapon as BaseProjectile;
            if (projectile == null) return true;

            // Get the Item from the weapon if possible (requires player context)
            var ownerPlayer = projectile.GetOwnerPlayer();
            if (ownerPlayer == null) return true; // No player context, assume allowed

            Item activeItem = ownerPlayer.GetActiveItem();
            if (activeItem == null || activeItem.GetHeldEntity() != weapon) return true; // Not the active weapon

            // Check the item's contents for attachments
            if (activeItem.contents != null && activeItem.contents.itemList != null)
            {
                foreach (Item mod in activeItem.contents.itemList)
                {
                    if (!IsAttachmentAllowed(mod.info.shortname)) return false;
                }
            }
            return true;
        }

        private Vector3 GetRandomSpawnInBiome(string biome)
        {
            switch (biome.ToLower())
            {
                case "snow":
                    if (config.biomeSettings.snowPositions.Count == 0) return Vector3.zero;
                    return config.biomeSettings.snowPositions[UnityEngine.Random.Range(0, config.biomeSettings.snowPositions.Count)];
                case "desert":
                    if (config.biomeSettings.desertPositions.Count == 0) return Vector3.zero;
                    return config.biomeSettings.desertPositions[UnityEngine.Random.Range(0, config.biomeSettings.desertPositions.Count)];
                case "grass":
                    if (config.biomeSettings.grassPositions.Count == 0) return Vector3.zero;
                    return config.biomeSettings.grassPositions[UnityEngine.Random.Range(0, config.biomeSettings.grassPositions.Count)];
                default:
                    if (config.biomeSettings.grassPositions.Count == 0) return Vector3.zero;
                    return config.biomeSettings.grassPositions[UnityEngine.Random.Range(0, config.biomeSettings.grassPositions.Count)];
            }
        }

        private Vector3 GetRandomPlayerSpawnInBiome(string biome)
        {
            string biomeLower = biome.ToLower();
            if (playerSpawnData.BiomePlayerSpawns.ContainsKey(biomeLower) && playerSpawnData.BiomePlayerSpawns[biomeLower].Count > 0)
            {
                return playerSpawnData.BiomePlayerSpawns[biomeLower][UnityEngine.Random.Range(0, playerSpawnData.BiomePlayerSpawns[biomeLower].Count)];
            }
            return GetRandomSpawnInBiome(biome); // Fallback to config spawn if no player spawn is set
        }
        #endregion

        #region Roam Validation and Cleanup

        // Periodic validation to ensure roams are ending properly
        private void ValidateActiveRoams()
        {
            if (activeRoams.Count == 0) return;

            var currentTime = UnityEngine.Time.realtimeSinceStartup;
            var roamsToCheck = activeRoams.ToList();

            if (config.debug)
                Puts($"[Roam Validation] Checking {roamsToCheck.Count} active roams");

            foreach (var roam in roamsToCheck)
            {
                if (roam == null)
                {
                    activeRoams.Remove(roam);
                    Puts("[Roam Validation] Removed null roam from active list");
                    continue;
                }

                // Enhanced stuck roam detection
                bool isStuck = false;
                string stuckReason = "";

                // Check if roam should have ended but hasn't (reduced grace period)
                if (roam.roamEndTime > 0 && currentTime > roam.roamEndTime + 15f) // 15 second grace period
                {
                    isStuck = true;
                    stuckReason = $"exceeded end time by {currentTime - roam.roamEndTime:F1} seconds";
                }

                // Check if roam time is negative (should have ended)
                if (roam.roamTime <= -10f) // 10 seconds past zero
                {
                    isStuck = true;
                    stuckReason = $"roam time is {roam.roamTime:F1} (should have ended)";
                }

                // Check if roam has been disabled but still exists
                if (!roam.enabled && roam.gameObject != null)
                {
                    isStuck = true;
                    stuckReason = "roam is disabled but still exists";
                }

                if (isStuck)
                {
                    Puts($"[Roam Validation] Found stuck roam: {stuckReason} - force ending");

                    // Immediate force cleanup
                    ForceEndStuckRoam(roam);
                }
                else
                {
                    // Validate player tracking consistency for active roams
                    ValidateRoamPlayerTracking(roam);
                }
            }

            // Additional cleanup - remove any roams that are no longer in the scene
            CleanupOrphanedRoams();
        }

        private void ForceEndStuckRoam(AutomaticRoamBubbleComp roam)
        {
            try
            {
                if (config.debug)
                    Puts($"[Roam Validation] Force ending stuck roam");

                // Set flags to prevent normal ending logic
                roam.hasEnded = true;
                roam.enabled = false;

                // Clean up players immediately
                var playersInThisRoam = playersInBubble.Where(kvp => kvp.Value == roam).ToList();
                foreach (var kvp in playersInThisRoam)
                {
                    playersInBubble.Remove(kvp.Key);
                    BasePlayer player = BasePlayer.Find(kvp.Key);
                    if (player != null)
                    {
                        CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                    }
                }

                // Cancel all invokes
                roam.CancelInvoke();

                // Remove from active roams
                activeRoams.Remove(roam);

                // Destroy the roam
                roam.DeleteCircle();
                UnityEngine.Object.DestroyImmediate(roam);

                Puts($"[Roam Validation] Successfully force ended stuck roam");
            }
            catch (System.Exception ex)
            {
                Puts($"[Roam Validation] Error force ending roam: {ex.Message}");
                // Last resort - just remove from tracking
                if (activeRoams.Contains(roam))
                {
                    activeRoams.Remove(roam);
                }
            }
        }

        private void ValidateRoamPlayerTracking(AutomaticRoamBubbleComp roam)
        {
            var playersInThisRoam = playersInBubble.Where(kvp => kvp.Value == roam).ToList();
            foreach (var kvp in playersInThisRoam)
            {
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player == null || !player.IsConnected)
                {
                    // Remove disconnected players from tracking
                    playersInBubble.Remove(kvp.Key);
                    if (config.debug)
                        Puts($"[Roam Validation] Removed disconnected player {kvp.Key} from roam tracking");
                }
            }
        }

        private void CleanupOrphanedRoams()
        {
            // Find roams in activeRoams that no longer exist in the scene
            var orphanedRoams = activeRoams.Where(roam => roam == null || roam.gameObject == null).ToList();

            foreach (var orphan in orphanedRoams)
            {
                activeRoams.Remove(orphan);
                Puts("[Roam Validation] Removed orphaned roam from active list");
            }

            // Also clean up any player tracking that points to non-existent roams
            var orphanedPlayers = playersInBubble.Where(kvp => kvp.Value == null || !activeRoams.Contains(kvp.Value)).ToList();

            foreach (var kvp in orphanedPlayers)
            {
                playersInBubble.Remove(kvp.Key);
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player != null)
                {
                    CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                }
                if (config.debug)
                    Puts($"[Roam Validation] Cleaned up orphaned player tracking for {kvp.Key}");
            }
        }

        // Aggressive validation for critical roam ending issues
        private void AggressiveRoamValidation()
        {
            if (activeRoams.Count == 0) return;

            var currentTime = UnityEngine.Time.realtimeSinceStartup;
            var criticalIssues = 0;

            // Check for roams that are severely stuck (longer grace period)
            var roamsToCheck = activeRoams.ToList();
            foreach (var roam in roamsToCheck)
            {
                if (roam == null)
                {
                    activeRoams.Remove(roam);
                    criticalIssues++;
                    continue;
                }

                // More aggressive detection for severely stuck roams
                bool isCritical = false;

                // Roam has been past end time for over 60 seconds
                if (roam.roamEndTime > 0 && currentTime > roam.roamEndTime + 60f)
                {
                    isCritical = true;
                    Puts($"[Critical] Roam severely stuck - {currentTime - roam.roamEndTime:F1}s past end time");
                }

                // Roam time is very negative
                if (roam.roamTime <= -60f)
                {
                    isCritical = true;
                    Puts($"[Critical] Roam time severely negative: {roam.roamTime:F1}");
                }

                // Roam has hasEnded flag but still exists
                if (roam.hasEnded && roam.gameObject != null)
                {
                    isCritical = true;
                    Puts($"[Critical] Roam marked as ended but still exists");
                }

                if (isCritical)
                {
                    criticalIssues++;
                    Puts($"[Critical] Force destroying critically stuck roam");

                    // Immediate nuclear cleanup
                    try
                    {
                        // Remove all players immediately
                        var playersInThisRoam = playersInBubble.Where(kvp => kvp.Value == roam).ToList();
                        foreach (var kvp in playersInThisRoam)
                        {
                            playersInBubble.Remove(kvp.Key);
                            BasePlayer player = BasePlayer.Find(kvp.Key);
                            if (player != null)
                            {
                                CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                            }
                        }

                        // Remove from active roams
                        activeRoams.Remove(roam);

                        // Nuclear destruction
                        roam.CancelInvoke();
                        roam.enabled = false;
                        roam.DeleteCircle();
                        UnityEngine.Object.DestroyImmediate(roam.gameObject);

                        Puts($"[Critical] Successfully destroyed critically stuck roam");
                    }
                    catch (System.Exception ex)
                    {
                        Puts($"[Critical] Error during nuclear cleanup: {ex.Message}");
                        // Last resort - just remove from tracking
                        if (activeRoams.Contains(roam))
                        {
                            activeRoams.Remove(roam);
                        }
                    }
                }
            }

            if (criticalIssues > 0)
            {
                Puts($"[Critical] Aggressive validation fixed {criticalIssues} critical issues");
            }
        }

        // Enhanced roam ending cleanup methods
        private void CleanupPlayersFromRoam(AutomaticRoamBubbleComp roam)
        {
            if (config.debug)
                Puts($"[Roam Debug] Cleaning up players from roam");

            // Get all players that should be in this roam
            var playersInThisRoam = playersInBubble.Where(kvp => kvp.Value == roam).ToList();

            foreach (var kvp in playersInThisRoam)
            {
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player != null)
                {
                    // Remove player from roam tracking
                    playersInBubble.Remove(kvp.Key);

                    // Destroy any UI elements
                    CuiHelper.DestroyUi(player, "AwakenRoamsUI");

                    if (config.debug)
                        Puts($"[Roam Debug] Cleaned up player {player.displayName} from roam");
                }
                else
                {
                    // Player is offline, just remove from tracking
                    playersInBubble.Remove(kvp.Key);

                    if (config.debug)
                        Puts($"[Roam Debug] Removed offline player {kvp.Key} from roam tracking");
                }
            }
        }

        private void PerformFinalCleanup(AutomaticRoamBubbleComp roam)
        {
            if (config.debug)
                Puts($"[Roam Debug] Performing final roam cleanup");

            // Cancel all invokes to prevent any lingering timers
            roam.CancelInvoke();

            // Ensure this roam is removed from active roams list
            if (activeRoams.Contains(roam))
            {
                activeRoams.Remove(roam);
                if (config.debug)
                    Puts($"[Roam Debug] Removed roam from active roams list");
            }

            // Clear any remaining data structures
            if (roam.teamStats != null)
            {
                roam.teamStats.Clear();
            }

            if (roam.weaponCounts != null)
            {
                roam.weaponCounts.Clear();
            }

            // Validate that all players have been properly removed
            var remainingPlayers = playersInBubble.Where(kvp => kvp.Value == roam).ToList();
            if (remainingPlayers.Count > 0)
            {
                Puts($"[Roam Warning] Found {remainingPlayers.Count} players still tracked in ended roam - force cleaning");
                foreach (var kvp in remainingPlayers)
                {
                    playersInBubble.Remove(kvp.Key);
                    BasePlayer player = BasePlayer.Find(kvp.Key);
                    if (player != null)
                    {
                        CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                    }
                }
            }

            if (config.debug)
                Puts($"[Roam Debug] Final cleanup completed");
        }

        private void ForceEndRoamProcesses(AutomaticRoamBubbleComp roam)
        {
            if (config.debug)
                Puts($"[Roam Debug] Force ending roam processes");

            // Cancel all invokes immediately
            roam.CancelInvoke();

            // Stop any timers
            if (roam.decayTimer != null)
            {
                roam.decayTimer.Destroy();
                roam.decayTimer = null;
            }

            // Disable the component to stop Update() calls
            roam.enabled = false;

            if (config.debug)
                Puts($"[Roam Debug] Roam processes force ended");
        }

        private void DestroyRoamSafely(AutomaticRoamBubbleComp roam)
        {
            if (config.debug)
                Puts($"[Roam Debug] Safely destroying roam");

            try
            {
                // First attempt - normal destruction
                roam.DeleteCircle();
                UnityEngine.Object.DestroyImmediate(roam.gameObject);

                if (config.debug)
                    Puts($"[Roam Debug] Roam destroyed successfully");
            }
            catch (System.Exception ex)
            {
                Puts($"[Roam Warning] Normal destruction failed: {ex.Message}, attempting force destruction");

                // Second attempt - force destruction
                try
                {
                    // Force remove from active roams first
                    if (activeRoams.Contains(roam))
                    {
                        activeRoams.Remove(roam);
                    }

                    // Try to destroy the circle entities manually
                    if (roam.innerSpheres != null)
                    {
                        foreach (var sphere in roam.innerSpheres)
                        {
                            if (sphere != null && !sphere.IsDestroyed)
                            {
                                sphere.Kill();
                            }
                        }
                        roam.innerSpheres.Clear();
                    }

                    // Destroy the game object
                    if (roam.gameObject != null)
                    {
                        UnityEngine.Object.DestroyImmediate(roam.gameObject);
                    }

                    if (config.debug)
                        Puts($"[Roam Debug] Force destruction completed");
                }
                catch (System.Exception ex2)
                {
                    Puts($"[Roam Error] Force destruction also failed: {ex2.Message}");
                    // At this point, just remove from tracking and hope for the best
                    if (activeRoams.Contains(roam))
                    {
                        activeRoams.Remove(roam);
                    }
                }
            }
        }

        #endregion

        #region Commands
        [Command("forceroam")]
        private void RoamCMD(IPlayer iPlayer, string command, string[] args)
        {
            // Add null safety check with stack trace for debugging
            if (iPlayer == null)
            {
                Puts("Error: RoamCMD called with null player - use StartRoamEvent hook instead for programmatic calls");
                Puts($"Stack trace: {System.Environment.StackTrace}");
                return;
            }

            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (player == null)
            {
                Puts("Error: Player object is null");
                return;
            }

            if (!permission.UserHasPermission(player.UserIDString, CallRoamPermission))
            {
                SendMessage(player, "NoPermission");
                return;
            }

            // Convert config.roamTime from "30m" format to seconds
            if (string.IsNullOrEmpty(config?.roamTime))
            {
                SendMessage(player, "Error: Roam time not configured properly");
                Puts("Error: config.roamTime is null or empty");
                return;
            }

            string roamTimeStr = config.roamTime.ToLower();
            int timeInSeconds;
            try
            {
                if (roamTimeStr.EndsWith("m"))
                {
                    int minutes = int.Parse(roamTimeStr.TrimEnd('m'));
                    timeInSeconds = minutes * 60; // Convert minutes to seconds
                }
                else
                {
                    timeInSeconds = int.Parse(roamTimeStr); // Fallback if it's just a number
                }
            }
            catch (Exception ex)
            {
                SendMessage(player, "Error: Invalid roam time format in config");
                Puts($"Error parsing roam time '{roamTimeStr}': {ex.Message}");
                return;
            }

            // Determine the biome (default to config if not provided)
            if (config?.biomeSettings == null || string.IsNullOrEmpty(config.biomeSettings.defaultBiome))
            {
                SendMessage(player, "Error: Biome settings not configured properly");
                Puts("Error: config.biomeSettings or defaultBiome is null");
                return;
            }

            string biome = config.biomeSettings.defaultBiome.ToLower();
            if (args.Length > 0)
            {
                biome = args[0].ToLower();
                if (!config.biomeSettings.allowedBiomes.Contains(biome, StringComparer.OrdinalIgnoreCase))
                {
                    SendMessage(player, $"Invalid biome. Allowed biomes are: {string.Join(", ", config.biomeSettings.allowedBiomes)}");
                    return;
                }
            }

            Vector3 position = GetRandomSpawnInBiome(biome);
            if (position == Vector3.zero)
            {
                SendMessage(player, "No spawn locations set for this biome. Use /setlocation to set locations.");
                return;
            }

            var bubbleObject = new GameObject();
            if (bubbleObject == null)
            {
                SendMessage(player, "Error: Failed to create roam bubble object");
                Puts("Error: Failed to create new GameObject");
                return;
            }

            var bubbleComp = bubbleObject.AddComponent<AutomaticRoamBubbleComp>();
            if (bubbleComp == null)
            {
                SendMessage(player, "Error: Failed to create roam bubble component");
                Puts("Error: Failed to add AutomaticRoamBubbleComp");
                return;
            }

            bubbleComp.CreateBubble(position, config.sphereRadius, biome, timeInSeconds);

            if (config.messages?.sendChatMessageOnRoam == true)
                SendBigGlobalMessage(string.Format(this.lang.GetMessage("RoamStartedChat", this), biome, timeInSeconds));
        }

        [Command("roams")]
        private void RoamsCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            // Check if player is in bubble dictionary
            if (playersInBubble.ContainsKey(player.UserIDString))
            {
                // Double-check if player is actually within a roam bubble
                AutomaticRoamBubbleComp roamBubble = playersInBubble[player.UserIDString];
                if (roamBubble != null && roamBubble.innerCollider != null)
                {
                    // Verify player is actually within the bubble's bounds
                    float distance = Vector3.Distance(player.transform.position, roamBubble.transform.position);
                    if (distance <= config.sphereRadius)
                    {
                        SendMessage(player, "AlreadyInRoam");
                        return;
                    }
                    else
                    {
                        // Player is not actually in the bubble, remove them from the dictionary
                        playersInBubble.Remove(player.UserIDString);
                        CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                        if (config.debug)
                            Puts($"[Roam Debug] Cleaned up stale entry for {player.displayName} - not actually in bubble");
                    }
                }
                else
                {
                    // Roam bubble is null or invalid, remove player from dictionary
                    playersInBubble.Remove(player.UserIDString);
                    CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                    if (config.debug)
                        Puts($"[Roam Debug] Cleaned up invalid roam bubble entry for {player.displayName}");
                }
            }

            if (activeRoams.Count == 0)
            {
                SendMessage(player, "There isn’t a roam active.");
                return;
            }

            AutomaticRoamBubbleComp targetBubble = activeRoams.FirstOrDefault();
            if (targetBubble == null) return;

            string biome = targetBubble.biome.ToLower();
            Vector3 bubblePosition = targetBubble.transform.position;
            float bubbleRadius = targetBubble.innerCollider.radius;
            Vector3 spawnPosition;

            bool spawnNearTeam = args.Length > 0 && args[0].ToLower() == "team";

            if (spawnNearTeam)
            {
                // Try to find a teammate within the bubble
                BasePlayer teammate = FindTeammateInBubble(player, targetBubble);
                if (teammate != null)
                {
                    // Spawn near teammate
                    spawnPosition = FindValidSpawnPosition(teammate.transform.position, 5f, bubblePosition, biome);
                    if (spawnPosition != Vector3.zero)
                    {
                        player.Teleport(spawnPosition);
                        SendMessage(player, $"Teleported near your teammate in the {biome} roam bubble!");
                        return;
                    }
                }
                // If no teammate found or no valid position near teammate, fall back to random spawn
                SendMessage(player, "No teammates found in the roam bubble, using random spawn instead.");
            }

            // Default random spawn behavior
            spawnPosition = GetRandomPlayerSpawnInBiome(biome);
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(spawnPosition);
            spawnPosition.y = terrainHeight;

            if (Vector3.Distance(spawnPosition, bubblePosition) <= bubbleRadius && TerrainMeta.WaterMap.GetHeight(spawnPosition) < terrainHeight)
            {
                player.Teleport(spawnPosition);
                SendMessage(player, $"Teleported to a random spot in the {biome} roam bubble!");
            }
            else
            {
                spawnPosition = FindValidSpawnPosition(bubblePosition, bubbleRadius, Vector3.zero, biome);
                if (spawnPosition != Vector3.zero)
                {
                    player.Teleport(spawnPosition);
                    SendMessage(player, $"Teleported to a random spot in the {biome} roam bubble (adjusted position)!");
                }
                else
                {
                    SendMessage(player, "Failed to find a valid spawn location within the roam bubble.");
                }
            }
        }

        [Command("setspawns")]
        private void SetSpawnsCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, SetLocationPermission))
            {
                SendMessage(player, "NoPermission");
                return;
            }

            if (args.Length != 1 || !config.biomeSettings.allowedBiomes.Contains(args[0], StringComparer.OrdinalIgnoreCase))
            {
                SendMessage(player, "InvalidBiome", string.Join(", ", config.biomeSettings.allowedBiomes));
                return;
            }

            string biome = args[0].ToLower();
            Vector3 position = player.transform.position;
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            position.y = terrainHeight;

            if (!playerSpawnData.BiomePlayerSpawns.ContainsKey(biome))
            {
                playerSpawnData.BiomePlayerSpawns[biome] = new List<Vector3>();
            }

            if (!playerSpawnData.BiomePlayerSpawns[biome].Contains(position))
            {
                playerSpawnData.BiomePlayerSpawns[biome].Add(position);
                SavePlayerSpawnData();
                SendMessage(player, "SpawnSet", biome, position.ToString());
            }
            else
            {
                SendMessage(player, "This position is already set as a player spawn for the {0} biome.", biome);
            }
        }

        [Command("clearspawns")]
        private void ClearSpawnsCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, SetLocationPermission))
            {
                SendMessage(player, "NoPermission");
                return;
            }

            // If no arguments provided, clear all spawns for all biomes
            if (args.Length == 0)
            {
                int totalCleared = 0;
                foreach (var biome in config.biomeSettings.allowedBiomes)
                {
                    string biomeLower = biome.ToLower();
                    if (playerSpawnData.BiomePlayerSpawns.ContainsKey(biomeLower))
                    {
                        int count = playerSpawnData.BiomePlayerSpawns[biomeLower].Count;
                        playerSpawnData.BiomePlayerSpawns[biomeLower].Clear();
                        totalCleared += count;
                    }
                }

                SavePlayerSpawnData();
                SendMessage(player, "AllSpawnsCleared", totalCleared);
                return;
            }

            // If biome argument provided, clear spawns for that specific biome
            if (args.Length == 1)
            {
                if (!config.biomeSettings.allowedBiomes.Contains(args[0], StringComparer.OrdinalIgnoreCase))
                {
                    SendMessage(player, "InvalidBiome", string.Join(", ", config.biomeSettings.allowedBiomes));
                    return;
                }

                string biome = args[0].ToLower();
                int clearedCount = 0;

                if (playerSpawnData.BiomePlayerSpawns.ContainsKey(biome))
                {
                    clearedCount = playerSpawnData.BiomePlayerSpawns[biome].Count;
                    playerSpawnData.BiomePlayerSpawns[biome].Clear();
                }

                SavePlayerSpawnData();
                SendMessage(player, "SpawnsCleared", clearedCount, biome);
                return;
            }

            // Invalid arguments
            player.ChatMessage($"<color=#d4af37>[Roam Bubble]</color> Usage: /clearspawns [biome] - Use without arguments to clear all spawns, or specify a biome to clear spawns for that biome only.");
            player.ChatMessage($"<color=#d4af37>[Roam Bubble]</color> Available biomes: {string.Join(", ", config.biomeSettings.allowedBiomes)}");
        }

        [Command("setlocation")]
        private void SetLocationCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, SetLocationPermission))
            {
                SendMessage(player, "NoPermission");
                return;
            }

            if (args.Length != 1)
            {
                SendMessage(player, "InvalidBiome", string.Join(", ", config.biomeSettings.allowedBiomes));
                return;
            }

            string biome = args[0].ToLower();
            if (!config.biomeSettings.allowedBiomes.Contains(biome, StringComparer.OrdinalIgnoreCase))
            {
                SendMessage(player, "InvalidBiome", string.Join(", ", config.biomeSettings.allowedBiomes));
                return;
            }

            Vector3 position = player.transform.position;
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            position.y = terrainHeight; // Ensure the position is on land

            // Add or update the spawn location for the biome
            switch (biome)
            {
                case "snow":
                    if (config.biomeSettings.snowPositions.Count == 0 || !config.biomeSettings.snowPositions.Contains(position))
                        config.biomeSettings.snowPositions.Add(position);
                    else
                        config.biomeSettings.snowPositions[config.biomeSettings.snowPositions.IndexOf(position)] = position;
                    break;
                case "desert":
                    if (config.biomeSettings.desertPositions.Count == 0 || !config.biomeSettings.desertPositions.Contains(position))
                        config.biomeSettings.desertPositions.Add(position);
                    else
                        config.biomeSettings.desertPositions[config.biomeSettings.desertPositions.IndexOf(position)] = position;
                    break;
                case "grass":
                    if (config.biomeSettings.grassPositions.Count == 0 || !config.biomeSettings.grassPositions.Contains(position))
                        config.biomeSettings.grassPositions.Add(position);
                    else
                        config.biomeSettings.grassPositions[config.biomeSettings.grassPositions.IndexOf(position)] = position;
                    break;
            }

            SaveConfig();
            SendMessage(player, "LocationSet", biome, position.ToString());
        }

        [Command("reloadimages")]
        private void ReloadImagesCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            LoadImages();
            player.Reply("Reloading images from config...");
        }

        [Command("checkimage")]
        private void CheckImageCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Logo System Status ===");
            player.Reply($"Config logo URL: {config?.ui?.logoImageURL ?? "null"}");
            player.Reply($"Logo validated: {!string.IsNullOrEmpty(cachedLogoUrl)}");
            player.Reply($"Currently validating: {isDownloadingLogo}");

            if (!string.IsNullOrEmpty(cachedLogoUrl))
            {
                player.Reply($"Validated logo URL: {cachedLogoUrl}");
                player.Reply("✅ Logo URL is working and validated");
            }

            if (!string.IsNullOrEmpty(config?.ui?.logoImageURL))
            {
                string logoUrl = config.ui.logoImageURL;

                // Check if the URL contains HTML color tags
                if (logoUrl.Contains("<color") || logoUrl.Contains("</color>"))
                {
                    player.Reply("❌ ERROR: Logo URL contains HTML color tags instead of a valid image URL!");
                    player.Reply("This is causing the UI display issues you're seeing.");
                    player.Reply("Please update your config file to use a proper image URL like:");
                    player.Reply("\"logoImageURL\": \"https://cdn.awakenrust.com/awaken_roams.png\"");
                    player.Reply("Or set it to an empty string \"\" to disable the logo.");
                }
                else if (logoUrl.StartsWith("http://") || logoUrl.StartsWith("https://"))
                {
                    player.Reply("✅ Logo URL is configured correctly");
                    player.Reply("Using URL validation system to prevent glitching");
                }
                else
                {
                    player.Reply("⚠️ Logo URL doesn't start with http:// or https:// - may not load properly");
                }
            }
            else
            {
                player.Reply("❌ No logo URL configured in config");
            }
        }

        [Command("refreshlogo")]
        private void RefreshLogoCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Refreshing Logo Validation ===");

            // Clear current validation
            cachedLogoUrl = null;
            isDownloadingLogo = false;

            player.Reply("✅ Cleared existing logo validation");

            // Re-validate logo URL
            CacheLogoLocally();

            if (isDownloadingLogo)
            {
                player.Reply("🔄 Started validating logo from configured URL");
                player.Reply("Logo will be validated and ready for use");
                player.Reply("This ensures the logo loads properly without glitching");
            }
            else
            {
                player.Reply("❌ Failed to start logo validation - check URL configuration");
            }
        }

        [Command("checkweapons")]
        private void CheckWeaponsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            player.Reply("=== Weapon Detection Debug ===");

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You are not in a roam bubble");
                return;
            }

            var roamBubble = playersInBubble[basePlayer.UserIDString];
            if (roamBubble == null)
            {
                player.Reply("❌ Could not find roam bubble component");
                return;
            }

            // Show current weapon counts
            if (roamBubble.weaponCounts.ContainsKey(basePlayer.UserIDString))
            {
                var weaponCount = roamBubble.weaponCounts[basePlayer.UserIDString];
                player.Reply($"Current counts - AKs: {weaponCount.akCount}, M2s: {weaponCount.m2Count}");
            }
            else
            {
                player.Reply("❌ No weapon count data found for you");
            }

            // List all items in inventory with shortnames
            player.Reply("=== Your Inventory Items ===");
            List<Item> allItems = new List<Item>();

            if (basePlayer.inventory?.containerMain != null)
                allItems.AddRange(basePlayer.inventory.containerMain.itemList.Where(i => i != null));
            if (basePlayer.inventory?.containerBelt != null)
                allItems.AddRange(basePlayer.inventory.containerBelt.itemList.Where(i => i != null));
            if (basePlayer.inventory?.containerWear != null)
                allItems.AddRange(basePlayer.inventory.containerWear.itemList.Where(i => i != null));

            foreach (var item in allItems)
            {
                if (item?.info != null)
                {
                    string shortname = item.info.shortname;
                    bool isAK = shortname == "rifle.ak" || shortname.Contains("rifle.ak");
                    bool isM2 = shortname == "lmg.m249" || shortname.Contains("lmg.m249") || shortname == "m249.entity" || shortname.Contains("m249.entity");

                    if (isAK || isM2)
                    {
                        player.Reply($"🔫 {item.info.displayName.english} - Shortname: '{shortname}' - Amount: {item.amount} - {(isAK ? "AK" : "M2")}");
                    }
                }
            }

            // Force recount
            roamBubble.CountPlayerWeapons(basePlayer);
            player.Reply("🔄 Forced weapon recount completed");
        }

        [Command("testui")]
        private void TestUICommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            player.Reply("=== UI System Debug ===");

            // Check if UI is enabled in config
            player.Reply($"UI Enabled in Config: {config?.ui?.useUI ?? false}");

            // Check if player is in a roam bubble
            bool inBubble = playersInBubble.ContainsKey(basePlayer.UserIDString);
            player.Reply($"Player in Bubble: {inBubble}");

            if (inBubble)
            {
                var roamBubble = playersInBubble[basePlayer.UserIDString];
                player.Reply($"Roam Bubble Found: {roamBubble != null}");

                if (roamBubble != null)
                {
                    // Test UI creation
                    player.Reply("🔄 Testing UI creation...");

                    try
                    {
                        CreateRoamUI(basePlayer);
                        player.Reply("✅ UI creation completed - check for any error messages above");
                    }
                    catch (Exception ex)
                    {
                        player.Reply($"❌ UI creation failed: {ex.Message}");
                    }

                    // Show stats that would be displayed
                    int kills = GetPlayerKills(basePlayer);
                    int deaths = GetPlayerDeaths(basePlayer);
                    int teams = CountTeamsInBubble(roamBubble);

                    player.Reply($"Stats - Kills: {kills}, Deaths: {deaths}, Teams: {teams}");
                    player.Reply($"Roam Time: {roamBubble.roamTime}");
                }
            }
            else
            {
                player.Reply("❌ You must be in a roam bubble to test UI");
                player.Reply("Enter a roam bubble and try again");
            }
        }

        [Command("cleanbubbles")]
        private void CleanBubblesCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            int bubbleCount = activeRoams.Count;
            int playerCount = playersInBubble.Count;

            // Clean up all active roam bubbles
            CleanupAllPlayerUIs();
            CleanupAllRoamBubbles();

            // Clear tracking data
            playersInBubble.Clear();

            // Also clean up any leftover bubbles
            CleanupLeftoverBubbles();

            player.Reply($"✅ Cleaned up {bubbleCount} active roam bubbles and removed UI from {playerCount} players.");
            Puts($"Admin {player.Name} manually cleaned up all roam bubbles.");
        }





        [Command("listallowedweapons")]
        private void ListAllowedWeaponsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Roam Bubble Weapon Configuration ===");

            if (config.weaponRestrictions.useAllowedWeaponsList)
            {
                player.Reply("✅ Using ALLOWED weapons list (whitelist mode)");
                player.Reply($"Total allowed weapons: {config.weaponRestrictions.allowedWeapons.Count}");
                player.Reply("--- Allowed Weapons ---");

                foreach (var weapon in config.weaponRestrictions.allowedWeapons)
                {
                    player.Reply($"• {weapon.Key} - {weapon.Value}");
                }
            }
            else
            {
                player.Reply("❌ Using BLOCKED weapons list (blacklist mode)");
                player.Reply($"Allow other weapons: {config.weaponRestrictions.allowOtherWeapons}");
                player.Reply($"Total blocked weapons: {config.weaponRestrictions.blockedWeapons.Count}");
                player.Reply("--- Blocked Weapons ---");

                foreach (var weapon in config.weaponRestrictions.blockedWeapons)
                {
                    player.Reply($"• {weapon.Key} - {weapon.Value}");
                }
            }

            player.Reply("=== Attachment Configuration ===");
            player.Reply($"Allow Other Attachments: {config.weaponRestrictions.allowOtherAttachments}");
            player.Reply($"Total blocked attachments: {config.weaponRestrictions.blockedAttachments.Count}");

            if (config.weaponRestrictions.blockedAttachments.Count > 0)
            {
                player.Reply("--- Blocked Attachments ---");
                foreach (var attachment in config.weaponRestrictions.blockedAttachments)
                {
                    player.Reply($"• {attachment.Key} - {attachment.Value}");
                }
            }

            player.Reply("=== Configuration Help ===");
            player.Reply("To switch to allowed weapons list mode:");
            player.Reply("Set 'Use Allowed Weapons List': true in config");
            player.Reply("Then only weapons in the 'Allowed Weapons' list will be permitted");
        }

        [Command("testdecay")]
        private void TestDecayCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You must be inside a roam bubble to test decay");
                return;
            }

            var roamBubble = playersInBubble[basePlayer.UserIDString];

            player.Reply("=== Decay System Test ===");
            player.Reply($"Decay Enabled: {config.decaySettings.enableAcceleratedDecay}");
            player.Reply($"Decay Multiplier: {config.decaySettings.decaySpeedMultiplier}x");
            player.Reply($"Check Interval: {config.decaySettings.decayCheckInterval}s");
            player.Reply($"Apply to All Structures: {config.decaySettings.applyToAllStructures}");
            player.Reply($"Apply to Barricades Only: {config.decaySettings.applyToBarricadesOnly}");
            player.Reply($"Debug Mode: {config.debug}");

            // Manually trigger decay check
            if (roamBubble != null)
            {
                player.Reply("--- Manual Decay Check ---");
                player.Reply($"Bubble Position: {roamBubble.transform.position}");
                player.Reply($"Bubble Radius: {roamBubble.innerCollider.radius}");

                // Use the improved decay detection method
                var structures = new HashSet<BaseEntity>();

                // Method 1: Physics.OverlapSphere with proper layer masks
                var colliders = Physics.OverlapSphere(roamBubble.transform.position, roamBubble.innerCollider.radius,
                    LayerMask.GetMask("Construction", "Deployed", "Default"));
                player.Reply($"Found {colliders.Length} colliders in bubble (Physics method)");

                foreach (var collider in colliders)
                {
                    var entity = collider.GetComponentInParent<BaseEntity>();
                    if (entity != null && roamBubble.ShouldAccelerateDecay(entity))
                    {
                        structures.Add(entity);
                    }
                }

                // Method 2: Check all entities in range
                var allEntities = BaseEntity.saveList.Where(e => e != null && !e.IsDestroyed &&
                    Vector3.Distance(e.transform.position, roamBubble.transform.position) <= roamBubble.innerCollider.radius);

                int entityMethodCount = 0;
                foreach (var entity in allEntities)
                {
                    entityMethodCount++;
                    if (roamBubble.ShouldAccelerateDecay(entity))
                    {
                        structures.Add(entity);
                    }
                }
                player.Reply($"Found {entityMethodCount} entities in range (Entity method)");

                player.Reply($"Total unique structures eligible for decay: {structures.Count}");

                if (structures.Count > 0)
                {
                    player.Reply("--- Structure Details ---");
                    foreach (var structure in structures.Take(10)) // Limit to first 10 for readability
                    {
                        var combatEntity = structure as BaseCombatEntity;
                        if (combatEntity != null)
                        {
                            player.Reply($"• {structure.GetType().Name} ({structure.ShortPrefabName}) - Health: {combatEntity.Health():F1}/{combatEntity.MaxHealth():F1}");
                        }
                    }

                    if (structures.Count > 10)
                    {
                        player.Reply($"... and {structures.Count - 10} more structures");
                    }

                    player.Reply("Applying decay to all structures...");
                    foreach (var structure in structures)
                    {
                        roamBubble.ApplyAcceleratedDecay(structure);
                    }
                    player.Reply("✅ Decay applied to all eligible structures!");
                }
                else
                {
                    player.Reply("❌ No structures found to decay");
                    player.Reply("This could mean:");
                    player.Reply("• No structures are built in the bubble");
                    player.Reply("• Decay settings exclude the structures present");
                    player.Reply("• The bubble detection is not working properly");
                }
            }
        }

        [Command("checkdecaytimer")]
        private void CheckDecayTimerCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            player.Reply("=== Decay Timer Status ===");
            player.Reply($"Global Decay Enabled: {config.decaySettings.enableAcceleratedDecay}");
            player.Reply($"Active Roam Bubbles: {activeRoams.Count}");

            if (activeRoams.Count == 0)
            {
                player.Reply("❌ No active roam bubbles");
                return;
            }

            for (int i = 0; i < activeRoams.Count; i++)
            {
                var roam = activeRoams[i];
                if (roam == null)
                {
                    player.Reply($"Roam {i + 1}: ❌ NULL");
                    continue;
                }

                player.Reply($"--- Roam Bubble {i + 1} ---");
                player.Reply($"Position: {roam.transform.position}");
                player.Reply($"Radius: {roam.innerCollider?.radius ?? 0}");
                player.Reply($"Time Remaining: {roam.roamTime:F1}s");

                bool hasDecayTimer = roam.decayTimer != null && !roam.decayTimer.Destroyed;
                player.Reply($"Decay Timer Active: {(hasDecayTimer ? "✅ YES" : "❌ NO")}");

                if (hasDecayTimer)
                {
                    player.Reply($"Timer Interval: {config.decaySettings.decayCheckInterval}s");
                    player.Reply($"Decay Multiplier: {config.decaySettings.decaySpeedMultiplier}x");
                }

                // Check if player is in this bubble
                if (playersInBubble.ContainsKey(basePlayer.UserIDString) && playersInBubble[basePlayer.UserIDString] == roam)
                {
                    player.Reply("👤 You are in this bubble");
                }
            }
        }

        [Command("restartdecay")]
        private void RestartDecayCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You must be inside a roam bubble to restart decay");
                return;
            }

            var roamBubble = playersInBubble[basePlayer.UserIDString];
            if (roamBubble == null)
            {
                player.Reply("❌ Roam bubble not found");
                return;
            }

            player.Reply("🔄 Restarting decay timer...");

            // Stop existing timer
            if (roamBubble.decayTimer != null && !roamBubble.decayTimer.Destroyed)
            {
                roamBubble.decayTimer.Destroy();
                roamBubble.decayTimer = null;
                player.Reply("✅ Stopped existing decay timer");
            }

            // Restart decay
            roamBubble.StartAcceleratedDecay();

            bool hasNewTimer = roamBubble.decayTimer != null && !roamBubble.decayTimer.Destroyed;
            player.Reply($"Decay timer restarted: {(hasNewTimer ? "✅ SUCCESS" : "❌ FAILED")}");

            if (hasNewTimer)
            {
                player.Reply($"Timer interval: {config.decaySettings.decayCheckInterval}s");
                player.Reply($"Decay multiplier: {config.decaySettings.decaySpeedMultiplier}x");
            }
        }

        [Command("testwalls")]
        private void TestWallsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            player.Reply("=== Converted High Walls Status ===");
            player.Reply($"Total converted high walls tracked: {convertedHighWalls.Count}");

            if (convertedHighWalls.Count > 0)
            {
                player.Reply("--- Converted High Wall Details ---");
                int count = 0;
                foreach (var wallId in convertedHighWalls.ToList())
                {
                    var wall = BaseNetworkable.serverEntities.Find(new NetworkableId(wallId)) as BaseEntity;
                    if (wall != null && !wall.IsDestroyed)
                    {
                        count++;
                        float distance = Vector3.Distance(basePlayer.transform.position, wall.transform.position);
                        var combatEntity = wall as BaseCombatEntity;
                        string healthInfo = combatEntity != null ? $"{combatEntity.Health():F1}/{combatEntity.MaxHealth():F1}" : "N/A";
                        player.Reply($"Wall {count}: Distance {distance:F1}m, Health: {healthInfo}, Owner: {wall.OwnerID}");
                    }
                    else
                    {
                        // Remove destroyed walls from tracking
                        convertedHighWalls.Remove(wallId);
                        player.Reply($"Removed destroyed wall ID {wallId} from tracking");
                    }
                }
                player.Reply($"Active converted walls: {count}");
            }
            else
            {
                player.Reply("❌ No converted high walls found");
                player.Reply("Try placing some barricades in a roam bubble to convert them to high walls");
            }
        }

        [Command("forcedecaywalls")]
        private void ForceDecayWallsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            player.Reply("=== Force Decay Converted Walls ===");
            player.Reply($"Tracked converted walls: {convertedHighWalls.Count}");

            if (convertedHighWalls.Count == 0)
            {
                player.Reply("❌ No converted walls to decay");
                return;
            }

            int decayedCount = 0;
            foreach (var wallId in convertedHighWalls.ToList())
            {
                var wall = BaseNetworkable.serverEntities.Find(new NetworkableId(wallId)) as BaseEntity;
                if (wall != null && !wall.IsDestroyed)
                {
                    var combatEntity = wall as BaseCombatEntity;
                    if (combatEntity != null)
                    {
                        float beforeHealth = combatEntity.Health();

                        // Apply heavy decay damage
                        var hitInfo = new HitInfo();
                        hitInfo.damageTypes.Set(Rust.DamageType.Decay, combatEntity.MaxHealth() * 0.5f); // 50% damage
                        hitInfo.DoHitEffects = false;
                        hitInfo.HitMaterial = 0;
                        hitInfo.PointStart = wall.transform.position;
                        hitInfo.PointEnd = wall.transform.position;
                        hitInfo.HitPositionWorld = wall.transform.position;

                        combatEntity.OnAttacked(hitInfo);

                        float afterHealth = combatEntity.Health();
                        player.Reply($"Wall {decayedCount + 1}: {beforeHealth:F1} → {afterHealth:F1} HP");
                        decayedCount++;
                    }
                }
                else
                {
                    convertedHighWalls.Remove(wallId);
                }
            }

            player.Reply($"✅ Applied decay to {decayedCount} converted walls");
        }

        [Command("testhighwalldecay")]
        private void TestHighWallDecayCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Test High Wall Decay System ===");
            player.Reply($"High Wall Decay Enabled: {config.highWallDecaySettings.EnableHighWallDecay}");
            player.Reply($"Decay Duration: {config.highWallDecaySettings.HighWallDecayDuration} seconds");
            player.Reply($"Tick Interval: {config.highWallDecaySettings.HighWallDecayTickInterval} seconds");
            player.Reply($"Start Delay: {config.highWallDecaySettings.StartDecayAfterRoamEnds} seconds");
            player.Reply($"Tracked Walls: {convertedHighWalls.Count}");
            player.Reply($"Active Decay Processes: {decayingHighWalls.Count}");

            if (convertedHighWalls.Count > 0)
            {
                player.Reply("--- Starting Test Decay ---");
                StartHighWallDecayAfterRoam();
                player.Reply("✅ Test decay process started!");
            }
            else
            {
                player.Reply("❌ No converted high walls to test with");
                player.Reply("Place some barricades in a roam bubble first");
            }
        }

        [Command("stophighwalldecay")]
        private void StopHighWallDecayCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Stop High Wall Decay ===");
            player.Reply($"Active decay processes: {decayingHighWalls.Count}");

            CleanupAllDecayingHighWalls();

            player.Reply("✅ All high wall decay processes stopped");
        }

        [Command("testbarricade")]
        private void TestBarricadeCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You must be inside a roam bubble to test barricade replacement");
                return;
            }

            player.Reply("=== Barricade Replacement Test ===");
            player.Reply("Place a wooden barricade and it should automatically convert to a high wall with you as the owner.");
            player.Reply("The high wall will have your UserID set as the OwnerID.");
            player.Reply($"Your UserID: {basePlayer.userID}");

            // Give the player a barricade to test with
            var barricadeItem = ItemManager.CreateByName("barricade.wood.cover", 1);
            if (barricadeItem != null)
            {
                basePlayer.GiveItem(barricadeItem);
                player.Reply("✅ Given you a wooden barricade to test with!");
                player.Reply("Place it and watch it convert to a high wall owned by you.");
            }
            else
            {
                player.Reply("❌ Failed to create barricade item");
            }
        }

        [Command("listattachments")]
        private void ListAttachmentsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Available Weapon Attachments ===");
            player.Reply($"Allow Other Attachments: {config.weaponRestrictions.allowOtherAttachments}");
            player.Reply($"Blocked Attachments: {config.weaponRestrictions.blockedAttachments.Count}");

            if (config.weaponRestrictions.blockedAttachments.Count > 0)
            {
                player.Reply("--- Blocked Attachments ---");
                foreach (var attachment in config.weaponRestrictions.blockedAttachments)
                {
                    player.Reply($"❌ {attachment.Key} - {attachment.Value}");
                }
            }

            player.Reply("--- Common Allowed Attachments ---");
            var allowedAttachments = new Dictionary<string, string>
            {
                { "weapon.mod.holosight", "Holographic Sight" },
                { "weapon.mod.simple", "Simple Sight" },
                { "weapon.mod.8x.scope", "8x Zoom Scope" },
                { "weapon.mod.16x.scope", "16x Zoom Scope" },
                { "weapon.mod.muzzlebrake", "Muzzle Brake" },
                { "weapon.mod.muzzleboost", "Muzzle Boost" },
                { "weapon.mod.flashlight", "Flashlight" },
                { "weapon.mod.lasersight", "Laser Sight" },
                { "weapon.mod.burstmodule", "Burst Fire Module" },
                { "weapon.mod.small.scope", "Small Scope" }
            };

            foreach (var attachment in allowedAttachments)
            {
                bool isBlocked = config.weaponRestrictions.blockedAttachments.ContainsKey(attachment.Key);
                string status = isBlocked ? "❌ BLOCKED" : "✅ ALLOWED";
                player.Reply($"{status} {attachment.Key} - {attachment.Value}");
            }

            player.Reply("=== Configuration Info ===");
            if (config.weaponRestrictions.allowOtherAttachments)
            {
                player.Reply("✅ All attachments are allowed except those in the blocked list");
            }
            else
            {
                player.Reply("❌ Only attachments NOT in the blocked list are allowed");
            }
        }

        [Command("teststats")]
        private void TestStatsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You must be inside a roam bubble to test stats");
                return;
            }

            var roamBubble = playersInBubble[basePlayer.UserIDString];

            player.Reply("=== Kill/Death Tracking Test ===");
            player.Reply($"Debug Mode: {config.debug}");
            player.Reply($"Your UserID: {basePlayer.userID}");

            // Show current player stats
            if (roamBubble.playerStats.ContainsKey(basePlayer.UserIDString))
            {
                var stats = roamBubble.playerStats[basePlayer.UserIDString];
                player.Reply($"Your Stats - Kills: {stats.kills}, Deaths: {stats.deaths}, Headshots: {stats.headshots}, Damage: {stats.damage}");
            }
            else
            {
                player.Reply("❌ No player stats found - this might be the issue!");
                // Initialize stats for testing
                roamBubble.playerStats[basePlayer.UserIDString] = new PlayerStats();
                player.Reply("✅ Initialized your player stats for testing");
            }

            // Show team stats
            string teamTag = GetTeamTag(basePlayer);
            player.Reply($"Your Team: {teamTag}");

            if (roamBubble.teamStats.ContainsKey(teamTag))
            {
                var teamStats = roamBubble.teamStats[teamTag];
                player.Reply($"Team Stats - Kills: {teamStats.kills}, Deaths: {teamStats.deaths}, Members: {teamStats.members.Count}");
            }
            else
            {
                player.Reply("❌ No team stats found");
            }

            // Show all players in bubble
            player.Reply("--- Players in Bubble ---");
            foreach (var kvp in playersInBubble.Where(kvp => kvp.Value == roamBubble))
            {
                var bubblePlayer = BasePlayer.Find(kvp.Key);
                if (bubblePlayer != null)
                {
                    var hasStats = roamBubble.playerStats.ContainsKey(kvp.Key);
                    player.Reply($"• {bubblePlayer.displayName} ({kvp.Key}) - Has Stats: {hasStats}");
                }
            }

            player.Reply("=== Testing Instructions ===");
            player.Reply("1. Kill another player in the roam bubble");
            player.Reply("2. Check console for debug messages");
            player.Reply("3. Run /teststats again to see updated stats");
            player.Reply("4. Check UI to see if kills/deaths display correctly");
        }



        [Command("endroam")]
        private void EndRoamCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You are not currently in a roam bubble");
                player.Reply("Move into the roam bubble you want to end and try again");
                return;
            }

            var roamBubble = playersInBubble[basePlayer.UserIDString];
            if (roamBubble == null)
            {
                player.Reply("❌ Error: Could not find roam bubble component");
                return;
            }

            // Get roam bubble info before ending
            Vector3 roamPosition = roamBubble.transform.position;
            float roamRadius = roamBubble.innerCollider.radius;
            int playersInRoam = playersInBubble.Count(kvp => kvp.Value == roamBubble);

            player.Reply("=== Ending Roam Bubble ===");
            player.Reply($"Position: {roamPosition}");
            player.Reply($"Radius: {roamRadius}m");
            player.Reply($"Players in roam: {playersInRoam}");

            // Confirm the action
            if (args.Length > 0 && args[0].ToLower() == "confirm")
            {
                // End the roam bubble
                try
                {
                    // Notify all players in the bubble
                    var playersToNotify = playersInBubble.Where(kvp => kvp.Value == roamBubble).ToList();
                    foreach (var kvp in playersToNotify)
                    {
                        BasePlayer bubblePlayer = BasePlayer.Find(kvp.Key);
                        if (bubblePlayer != null && bubblePlayer.IsConnected)
                        {
                            bubblePlayer.ChatMessage($"<color=#ff6500>🔴 ROAM ENDED</color> - The roam event has been ended by an admin");
                        }
                    }

                    // Destroy the roam bubble
                    roamBubble.DeleteCircle();
                    UnityEngine.Object.DestroyImmediate(roamBubble);

                    player.Reply("✅ Roam bubble ended successfully!");
                    player.Reply($"Notified {playersToNotify.Count} players about the roam ending");

                    if (config.debug)
                        Puts($"[Roam Debug] Admin {player.Name} ended roam at {roamPosition}");
                }
                catch (System.Exception ex)
                {
                    player.Reply($"❌ Error ending roam: {ex.Message}");
                    PrintError($"Error ending roam: {ex.Message}");
                }
            }
            else
            {
                player.Reply("⚠️ This will end the roam event and remove all players from it");
                player.Reply($"Type '/endroam confirm' to confirm ending this roam");
                player.Reply("This action cannot be undone!");
            }
        }

        [Command("listroams")]
        private void ListRoamsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Active Roam Bubbles ===");

            // Get all unique roam bubbles
            var uniqueRoams = playersInBubble.Values.Distinct().ToList();

            if (uniqueRoams.Count == 0)
            {
                player.Reply("❌ No active roam bubbles found");
                return;
            }

            player.Reply($"Found {uniqueRoams.Count} active roam bubble(s):");

            for (int i = 0; i < uniqueRoams.Count; i++)
            {
                var roam = uniqueRoams[i];
                if (roam == null || roam.gameObject == null) continue;

                Vector3 position = roam.transform.position;
                float radius = roam.innerCollider.radius;
                int playerCount = playersInBubble.Count(kvp => kvp.Value == roam);

                player.Reply($"--- Roam {i + 1} ---");
                player.Reply($"Position: {position.x:F1}, {position.y:F1}, {position.z:F1}");
                player.Reply($"Radius: {radius}m");
                player.Reply($"Players: {playerCount}");

                // List players in this roam
                var playersInThisRoam = playersInBubble.Where(kvp => kvp.Value == roam).ToList();
                if (playersInThisRoam.Count > 0)
                {
                    player.Reply("Players in roam:");
                    foreach (var kvp in playersInThisRoam.Take(5)) // Show max 5 players
                    {
                        BasePlayer roamPlayer = BasePlayer.Find(kvp.Key);
                        if (roamPlayer != null)
                        {
                            player.Reply($"  • {roamPlayer.displayName}");
                        }
                    }
                    if (playersInThisRoam.Count > 5)
                    {
                        player.Reply($"  ... and {playersInThisRoam.Count - 5} more");
                    }
                }
            }

            player.Reply("=== Commands ===");
            player.Reply("• /endroam - End the roam you're standing in");
            player.Reply("• /endroam confirm - Confirm ending the roam");
            player.Reply("• /listroams - Show this list again");
            player.Reply("• /fixroams - Force fix any stuck roams");
        }

        [Command("fixroams")]
        private void FixRoamsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Fixing Stuck Roams ===");

            var currentTime = UnityEngine.Time.realtimeSinceStartup;
            int stuckRoams = 0;
            int cleanedPlayers = 0;

            // Check for stuck roams
            var roamsToCheck = activeRoams.ToList();
            foreach (var roam in roamsToCheck)
            {
                if (roam == null) continue;

                bool isStuck = (roam.roamEndTime > 0 && currentTime > roam.roamEndTime + 15f) ||
                               (roam.roamTime <= -10f) ||
                               (!roam.enabled && roam.gameObject != null);

                if (isStuck)
                {
                    stuckRoams++;
                    player.Reply($"🔧 Fixing stuck roam at {roam.transform.position}");
                }
            }

            // Force run validation
            ValidateActiveRoams();

            // Additional cleanup
            var orphanedPlayers = playersInBubble.Where(kvp => kvp.Value == null || !activeRoams.Contains(kvp.Value)).ToList();
            if (orphanedPlayers.Count > 0)
            {
                foreach (var kvp in orphanedPlayers)
                {
                    playersInBubble.Remove(kvp.Key);
                    BasePlayer bubblePlayer = BasePlayer.Find(kvp.Key);
                    if (bubblePlayer != null)
                    {
                        CuiHelper.DestroyUi(bubblePlayer, "AwakenRoamsUI");
                    }
                    cleanedPlayers++;
                }
            }

            player.Reply("✅ Roam validation completed");
            player.Reply($"Stuck roams found and fixed: {stuckRoams}");
            player.Reply($"Orphaned players cleaned: {cleanedPlayers}");
            player.Reply($"Active roams after fix: {activeRoams.Count}");
            player.Reply($"Players in bubbles: {playersInBubble.Count}");
        }


        #endregion

        #region UI
        private string HexToCuiColor(string hex, int alpha = 100)
        {
            if (hex.StartsWith("#"))
                hex = hex.Substring(1);

            if (hex.Length != 6)
                return "1 1 1 1"; // Default to white if invalid

            try
            {
                int r = Convert.ToInt32(hex.Substring(0, 2), 16);
                int g = Convert.ToInt32(hex.Substring(2, 2), 16);
                int b = Convert.ToInt32(hex.Substring(4, 2), 16);
                float a = alpha / 100f;

                return $"{r / 255f} {g / 255f} {b / 255f} {a}";
            }
            catch
            {
                return "1 1 1 1"; // Default to white if conversion fails
            }
        }



        private void CreateRoamUI(BasePlayer player, float time = -1)
        {
            if (player == null || !player.IsConnected)
            {
                return;
            }

            // Destroy existing UI to prevent caching issues
            CuiHelper.DestroyUi(player, "AwakenRoamsUI");

            AutomaticRoamBubbleComp roamBubble;
            if (!playersInBubble.TryGetValue(player.UserIDString, out roamBubble))
            {
                return;
            }

            var container = new CuiElementContainer();

            // Main UI container
            container.Add(new CuiElement
            {
                Name = "AwakenRoamsUI",
                Parent = "Hud",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1"
                    }
                }
            });

            // Frame 2 - Main container (positioned next to health bar, bottom-right)
            container.Add(new CuiElement
            {
                Name = "Frame 2",
                Parent = "AwakenRoamsUI",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "1 0",
                        AnchorMax = "1 0",
                        OffsetMin = "-320 20",
                        OffsetMax = "-200 140"
                    }
                }
            });

            // Frame 3 - Stats container (positioned within Frame 2)
            container.Add(new CuiElement
            {
                Name = "Frame 3",
                Parent = "Frame 2",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1",
                        OffsetMin = "0 0",
                        OffsetMax = "0 0"
                    }
                }
            });

            // Awaken Roams Image - Direct URL method
            CreateLogo(container, "Frame 3");

            // Get player stats for display
            int playerKills = GetPlayerKills(player);
            int playerDeaths = GetPlayerDeaths(player);
            // Count teams based on players currently in bubble, not just team stats
            int totalTeams = Instance.CountTeamsInBubble(roamBubble);

            // Format time remaining
            string timeText = time >= 0 ? FormatTime(time) : "00:00";

            // KILLS text with dynamic data (positioned lower to avoid overlap, moved right 10px)
            container.Add(new CuiElement
            {
                Name = "KILLS {k}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"KILLS {playerKills}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.3",
                        AnchorMax = "0.5 0.4",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // DEATHS text with dynamic data (positioned lower to avoid overlap, moved right 10px)
            container.Add(new CuiElement
            {
                Name = "DEATHS {d}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"DEATHS {playerDeaths}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.2",
                        AnchorMax = "0.5 0.3",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // TEAMS text with dynamic data (positioned lower to avoid overlap, moved right 10px)
            container.Add(new CuiElement
            {
                Name = "TEAMS {k}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"TEAMS {totalTeams}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.1",
                        AnchorMax = "0.5 0.2",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // Time text with dynamic data (positioned lower to avoid overlap, moved right 10px)
            container.Add(new CuiElement
            {
                Name = "{time}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = timeText, Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.0",
                        AnchorMax = "0.5 0.1",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            try
            {
                CuiHelper.AddUi(player, container);
                if (config.debug)
                    Puts($"[UI Debug] Successfully created UI for player {player.displayName}");
            }
            catch (Exception ex)
            {
                PrintError($"[UI Error] Failed to create UI for player {player.displayName}: {ex.Message}");
                if (config.debug)
                    PrintError($"[UI Debug] Full exception: {ex}");
            }
        }

        private string FormatTime(float timeInSeconds)
        {
            TimeSpan timeSpan = TimeSpan.FromSeconds(timeInSeconds);
            return $"{timeSpan.Minutes.ToString("D2")}:{timeSpan.Seconds.ToString("D2")}";
        }

        private void UpdateTimerUI(BasePlayer player, float time)
        {
            if (player == null || !player.IsConnected)
            {
                return;
            }

            // Update the time text in the existing UI
            string timeText = FormatTime(time);
            CuiHelper.DestroyUi(player, "{time}");

            var container = new CuiElementContainer();

            container.Add(new CuiElement
            {
                Name = "{time}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = timeText, Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.0",
                        AnchorMax = "0.5 0.1",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            try
            {
                CuiHelper.AddUi(player, container);
            }
            catch (Exception ex)
            {
                // Handle or log the exception if needed
            }
        }

        private int GetPlayerKills(BasePlayer player)
        {
            if (player == null) return 0;
            AutomaticRoamBubbleComp bubble = playersInBubble.GetValueOrDefault(player.UserIDString);
            if (bubble != null && bubble.playerStats != null && bubble.playerStats.TryGetValue(player.UserIDString, out PlayerStats stats))
            {
                return stats.kills;
            }
            return 0;
        }

        private int GetPlayerDeaths(BasePlayer player)
        {
            if (player == null) return 0;
            AutomaticRoamBubbleComp bubble = playersInBubble.GetValueOrDefault(player.UserIDString);
            if (bubble != null && bubble.playerStats != null && bubble.playerStats.TryGetValue(player.UserIDString, out PlayerStats stats))
            {
                return stats.deaths;
            }
            return 0;
        }

        private BasePlayer FindTeammateInBubble(BasePlayer player, AutomaticRoamBubbleComp bubble)
        {
            // Use Clans to find clan members in the bubble
            if (Clans == null)
            {
                return null; // No teammates if Clans not available
            }

            var clanMembers = Clans?.Call("GetClanMembers", player.UserIDString);
            if (clanMembers != null && clanMembers is List<string> membersList)
            {
                foreach (var memberId in membersList)
                {
                    if (memberId == player.UserIDString) continue;

                    // Safely parse the member ID
                    if (ulong.TryParse(memberId, out ulong memberUlong))
                    {
                        BasePlayer teammate = BasePlayer.FindByID(memberUlong);
                        if (teammate != null && Vector3.Distance(teammate.transform.position, bubble.transform.position) <= bubble.innerCollider.radius)
                        {
                            return teammate;
                        }
                    }
                }
            }
            return null;
        }
        #endregion

        #region Get Spawn Locations
        private Vector3 FindValidSpawnPosition(Vector3 bubblePosition, float radius, Vector3 nearPosition, string biome)
        {
            int maxAttempts = 50; // Reduced attempts since we're doing minimal validation
            for (int i = 0; i < maxAttempts; i++)
            {
                Vector3 randomOffset = UnityEngine.Random.insideUnitSphere * radius;
                randomOffset.y = 0; // Keep on the horizontal plane
                Vector3 testPosition = bubblePosition + randomOffset;

                // If nearPosition is provided (for team member), prioritize positions close to it
                if (nearPosition != Vector3.zero)
                {
                    Vector3 direction = (nearPosition - bubblePosition).normalized;
                    testPosition = bubblePosition + (direction * UnityEngine.Random.Range(0f, radius * 0.8f));
                }

                // Ensure the position is within the bubble
                if (Vector3.Distance(testPosition, bubblePosition) > radius)
                {
                    continue; // Skip if outside the bubble
                }

                // Adjust position to terrain height
                float terrainHeight = TerrainMeta.HeightMap.GetHeight(testPosition);
                testPosition.y = terrainHeight;

                // SIMPLIFIED: Only check if position is above water - that's it!
                float waterHeight = TerrainMeta.WaterMap.GetHeight(testPosition);
                if (waterHeight < terrainHeight)
                {
                    if (config.debug)
                        Puts($"[Roam Debug] Found valid spawn position at {testPosition} (attempt {i + 1})");
                    return testPosition; // Found a valid position: on land, not in water, within bubble
                }
            }

            if (config.debug)
                Puts($"[Roam Debug] Failed to find valid spawn position after {maxAttempts} attempts");
            return Vector3.zero; // No valid position found after attempts
        }

        // Helper method to verify if a position is on land in the specified biome
        private bool IsPositionOnLandInBiome(Vector3 position, string biome)
        {
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            position.y = terrainHeight;

            // Enhanced water check - ensure position is well above water level
            float waterHeight = TerrainMeta.WaterMap.GetHeight(position);
            float landBuffer = 2f; // Require at least 2 units above water level

            if (waterHeight + landBuffer >= terrainHeight)
            {
                if (config.debug)
                    Puts($"[Roam Debug] Position rejected - too close to water (water: {waterHeight}, terrain: {terrainHeight})");
                return false;
            }

            // Additional biome-specific terrain validation
            if (!IsValidBiomeTerrain(position, biome))
            {
                if (config.debug)
                    Puts($"[Roam Debug] Position rejected - invalid biome terrain for {biome}");
                return false;
            }

            // Check for roads, monuments, or other invalid areas
            if (IsPositionNearInvalidArea(position))
            {
                if (config.debug)
                    Puts($"[Roam Debug] Position rejected - near invalid area (road/monument)");
                return false;
            }

            return true;
        }

        // Enhanced biome terrain validation
        private bool IsValidBiomeTerrain(Vector3 position, string biome)
        {
            // Get biome information at this position
            int biomeInt = (int)TerrainMeta.BiomeMap.GetBiome(position, 1);

            switch (biome.ToLower())
            {
                case "snow":
                    // Check if position is in arctic/tundra biome
                    return (biomeInt & 8) != 0; // Arctic biome flag
                case "desert":
                    // Check if position is in arid biome
                    return (biomeInt & 2) != 0; // Arid biome flag
                case "grass":
                    // Check if position is in temperate biome
                    return (biomeInt & 4) != 0; // Temperate biome flag
                default:
                    return true; // Allow any biome for unknown types
            }
        }

        // Check if position is near roads, monuments, or other invalid areas
        private bool IsPositionNearInvalidArea(Vector3 position)
        {
            float checkRadius = 50f; // Check 50m radius for invalid areas

            // Check for roads (simplified check using topology)
            int topology = TerrainMeta.TopologyMap.GetTopology(position);
            if ((topology & (int)TerrainTopology.Enum.Road) != 0)
            {
                return true; // Too close to road
            }

            // Check for monuments
            if (TerrainMeta.Path != null)
            {
                var monuments = TerrainMeta.Path.Monuments;
                if (monuments != null)
                {
                    foreach (var monument in monuments)
                    {
                        if (monument != null && Vector3.Distance(position, monument.transform.position) < checkRadius)
                        {
                            return true; // Too close to monument
                        }
                    }
                }
            }

            return false;
        }

        // Helper method to ensure the position is safe (not on water edges or cliffs) - more lenient
        private bool IsPositionSafe(Vector3 position, string biome)
        {
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            position.y = terrainHeight;

            // Check a smaller radius and be more lenient
            float checkRadius = 1f; // Reduced radius for less strict checking
            for (int i = 0; i < 4; i++) // Check only 4 points instead of 8
            {
                float angle = (2 * Mathf.PI * i) / 4;
                Vector3 checkPosition = position + new Vector3(Mathf.Cos(angle) * checkRadius, 0, Mathf.Sin(angle) * checkRadius);
                float checkHeight = TerrainMeta.HeightMap.GetHeight(checkPosition);
                checkPosition.y = checkHeight;

                // More lenient - allow bigger height differences and closer to water
                if (TerrainMeta.WaterMap.GetHeight(checkPosition) >= checkHeight || Mathf.Abs(checkHeight - terrainHeight) > 3f) // Allow 3m height difference
                {
                    return false;
                }
            }
            return true;
        }

        // Final spawn position validation
        private bool ValidateFinalSpawnPosition(Vector3 position)
        {
            // Double-check terrain height vs water height with more lenient requirements
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            float waterHeight = TerrainMeta.WaterMap.GetHeight(position);

            // Require at least 0.5 units above water (much more lenient)
            if (waterHeight + 0.5f >= terrainHeight)
            {
                if (config.debug)
                    Puts($"[Roam Debug] Final validation failed - insufficient height above water");
                return false;
            }

            // Check for topology issues (only reject major water areas)
            int topology = TerrainMeta.TopologyMap.GetTopology(position);

            // Only reject major water areas, allow cliffs and riverside
            if ((topology & (int)TerrainTopology.Enum.Lake) != 0 ||
                (topology & (int)TerrainTopology.Enum.Swamp) != 0)
            {
                if (config.debug)
                    Puts($"[Roam Debug] Final validation failed - water topology: {topology}");
                return false;
            }

            // Check slope - allow steeper slopes (up to 45 degrees)
            Vector3 normal = TerrainMeta.HeightMap.GetNormal(position);
            float slope = Vector3.Angle(Vector3.up, normal);
            if (slope > 45f) // Allow slopes up to 45 degrees
            {
                if (config.debug)
                    Puts($"[Roam Debug] Final validation failed - slope too steep: {slope} degrees");
                return false;
            }

            return true;
        }







        // Helper methods to count actual clan teams (not individual players)
        private int CountActualTeams(Dictionary<string, TeamStats> teamStats)
        {
            int clanTeams = 0;
            foreach (var teamEntry in teamStats)
            {
                string teamName = teamEntry.Key;
                var stats = teamEntry.Value;

                // Only count teams that have actually participated (kills OR deaths > 0)
                if (stats.kills > 0 || stats.deaths > 0)
                {
                    // Count as team if: multiple members OR verified clan tag
                    if (stats.members.Count > 1 || IsActualClanTag(teamName))
                    {
                        clanTeams++;
                    }
                }
            }
            return clanTeams;
        }

        private int CountActualTeamsFromGroups(List<TeamGroup> teamGroups)
        {
            int clanTeams = 0;
            foreach (var group in teamGroups)
            {
                string teamName = group.Team;
                int playerCount = group.Players.Count;
                int totalKills = group.TotalKills;
                int totalDeaths = group.TotalDeaths;

                // Only count teams that have actually participated (kills OR deaths > 0)
                if (totalKills > 0 || totalDeaths > 0)
                {
                    // Count as team if: multiple members OR verified clan tag
                    if (playerCount > 1 || IsActualClanTag(teamName))
                    {
                        clanTeams++;
                    }
                }
            }
            return clanTeams;
        }

        private bool IsActualClanTag(string teamName)
        {
            // Check if this is a clan name/tag (not an individual player name)
            if (string.IsNullOrEmpty(teamName)) return false;

            // Check if teamName looks like a clan tag (short, alphanumeric, no spaces)
            if (!teamName.Contains(" ") && teamName.Length >= 2 && teamName.Length <= 10)
            {
                // Check if it's all alphanumeric (typical clan tag pattern)
                if (teamName.All(c => char.IsLetterOrDigit(c)))
                {
                    return true;
                }
            }

            // Also check if any active player has this in their display name as a clan tag
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player.displayName.StartsWith($"[{teamName}]"))
                {
                    return true;
                }
            }

            // Fallback: Try Clans API if available
            if (Clans != null)
            {
                foreach (var player in BasePlayer.activePlayerList)
                {
                    // Check clan tag first
                    var clanTag = Clans.Call("GetClanTag", player.userID) as string;
                    if (!string.IsNullOrEmpty(clanTag) && clanTag.Equals(teamName, StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }

                    // Check clan name as fallback
                    var clan = Clans.Call("GetClan", player.userID);
                    if (clan != null)
                    {
                        var clanName = Clans.Call("GetClanName", clan) as string;
                        if (!string.IsNullOrEmpty(clanName) && clanName.Equals(teamName, StringComparison.OrdinalIgnoreCase))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        // New method to count teams based on players currently in bubble
        private int CountTeamsInBubble(AutomaticRoamBubbleComp roamBubble)
        {
            HashSet<string> uniqueTeams = new HashSet<string>();

            // Get all players currently in this bubble
            foreach (var kvp in playersInBubble.Where(kvp => kvp.Value == roamBubble))
            {
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player != null && player.IsConnected && !IsPlayerVanished(player))
                {
                    string teamTag = GetTeamTag(player);

                    // Only count actual clan tags, not individual player names
                    if (IsActualClanTag(teamTag))
                    {
                        uniqueTeams.Add(teamTag);
                    }
                }
            }

            return uniqueTeams.Count;
        }
        #endregion

        // Voting system integration for AwakenVotingSystem
        #region VotingSystemIntegration

        // Called by AwakenVotingSystem when a roam event is selected
        [HookMethod("StartRoamEvent")]
        public void StartRoamEvent(string biome = null, int duration = 0)
        {
            // Use provided parameters or fall back to config defaults
            string selectedBiome = !string.IsNullOrEmpty(biome) ? biome : config.biomeSettings.defaultBiome;
            int roamDuration = duration > 0 ? duration : ParseRoamTime(config.roamTime);

            // Validate biome
            if (!config.biomeSettings.allowedBiomes.Contains(selectedBiome, StringComparer.OrdinalIgnoreCase))
            {
                PrintError($"Invalid biome '{selectedBiome}' selected from voting system. Using default biome.");
                selectedBiome = config.biomeSettings.defaultBiome;
            }

            // Get spawn position for the selected biome
            Vector3 position = GetRandomSpawnInBiome(selectedBiome);
            if (position == Vector3.zero)
            {
                PrintError($"No spawn locations set for {selectedBiome} biome. Cannot start roam event.");
                return;
            }

            // Create the roam bubble
            var bubbleObject = new GameObject();
            var bubbleComp = bubbleObject.AddComponent<AutomaticRoamBubbleComp>();
            bubbleComp.CreateBubble(position, config.sphereRadius, selectedBiome, roamDuration);

            // Send start message
            if (config.messages.sendChatMessageOnRoam)
            {
                SendBigGlobalMessage(string.Format(this.lang.GetMessage("RoamStartedChat", this), selectedBiome, roamDuration));
            }

            Puts($"Roam event started via voting system: {selectedBiome} biome, {roamDuration} seconds duration");
        }

        // Called by AwakenVotingSystem to get available roam options
        [HookMethod("GetRoamOptions")]
        public Dictionary<string, object> GetRoamOptions()
        {
            var options = new Dictionary<string, object>();

            // Add biome options
            var biomes = new List<object>();
            foreach (string biome in config.biomeSettings.allowedBiomes)
            {
                // Check if biome has spawn locations
                Vector3 testPosition = GetRandomSpawnInBiome(biome);
                if (testPosition != Vector3.zero)
                {
                    biomes.Add(new Dictionary<string, object>
                    {
                        ["name"] = biome,
                        ["displayName"] = char.ToUpper(biome[0]) + biome.Substring(1), // Capitalize first letter
                        ["available"] = true
                    });
                }
            }

            options["biomes"] = biomes;
            options["defaultDuration"] = ParseRoamTime(config.roamTime);
            options["eventType"] = "roam";

            return options;
        }

        // Called by AwakenVotingSystem to check if roam can start
        [HookMethod("CanStartRoamEvent")]
        public bool CanStartRoamEvent()
        {
            // Check if there's already an active roam
            if (activeRoams.Count > 0)
            {
                return false;
            }

            // Check if at least one biome has spawn locations
            foreach (string biome in config.biomeSettings.allowedBiomes)
            {
                Vector3 testPosition = GetRandomSpawnInBiome(biome);
                if (testPosition != Vector3.zero)
                {
                    return true;
                }
            }

            return false;
        }

        #region API Methods for Voting System

        // API method to check if any roam events are currently active
        [HookMethod("API_IsEventActive")]
        public bool API_IsEventActive()
        {
            return activeRoams.Count > 0;
        }

        // API method to get detailed event status
        [HookMethod("API_GetEventStatus")]
        public Dictionary<string, object> API_GetEventStatus()
        {
            var status = new Dictionary<string, object>
            {
                ["IsActive"] = activeRoams.Count > 0,
                ["EventCount"] = activeRoams.Count,
                ["EventType"] = "roam",
                ["PluginName"] = "AutomaticRoamBubble"
            };

            if (activeRoams.Count > 0)
            {
                var activeRoam = activeRoams[0]; // Get first active roam
                status["TimeRemaining"] = activeRoam.roamTime;
                status["Location"] = activeRoam.transform.position;
                status["Biome"] = activeRoam.biome;
                status["ParticipantCount"] = playersInBubble.Count(kvp => kvp.Value == activeRoam && !IsPlayerVanished(BasePlayer.FindByID(ulong.Parse(kvp.Key))));
                status["Radius"] = activeRoam.innerCollider?.radius ?? config.sphereRadius;

                // Team statistics
                status["TeamCount"] = activeRoam.teamStats.Count;
                status["TotalKills"] = activeRoam.teamStats.Values.Sum(t => t.kills);
                status["TotalDeaths"] = activeRoam.teamStats.Values.Sum(t => t.deaths);
            }
            else
            {
                status["TimeRemaining"] = 0f;
                status["Location"] = Vector3.zero;
                status["Biome"] = "";
                status["ParticipantCount"] = 0;
                status["Radius"] = 0f;
                status["TeamCount"] = 0;
                status["TotalKills"] = 0;
                status["TotalDeaths"] = 0;
            }

            return status;
        }

        // API method to get all active events (for multiple roam support)
        [HookMethod("API_GetActiveEvents")]
        public List<Dictionary<string, object>> API_GetActiveEvents()
        {
            var events = new List<Dictionary<string, object>>();

            foreach (var roam in activeRoams)
            {
                if (roam == null) continue;

                var eventData = new Dictionary<string, object>
                {
                    ["EventType"] = "roam",
                    ["TimeRemaining"] = roam.roamTime,
                    ["Location"] = roam.transform.position,
                    ["Biome"] = roam.biome,
                    ["ParticipantCount"] = playersInBubble.Count(kvp => kvp.Value == roam && !IsPlayerVanished(BasePlayer.FindByID(ulong.Parse(kvp.Key)))),
                    ["Radius"] = roam.innerCollider?.radius ?? config.sphereRadius,
                    ["TeamCount"] = roam.teamStats.Count,
                    ["TotalKills"] = roam.teamStats.Values.Sum(t => t.kills),
                    ["TotalDeaths"] = roam.teamStats.Values.Sum(t => t.deaths),
                    ["StartTime"] = roam.initialRoamTime - roam.roamTime
                };

                events.Add(eventData);
            }

            return events;
        }

        // API method to force end all active roam events
        [HookMethod("API_EndAllEvents")]
        public bool API_EndAllEvents()
        {
            if (activeRoams.Count == 0) return false;

            var roamsToEnd = activeRoams.ToList();
            foreach (var roam in roamsToEnd)
            {
                if (roam != null)
                {
                    roam.roamTime = 0f; // This will trigger the end sequence in Update()
                }
            }

            return true;
        }

        // API method to get event configuration info
        [HookMethod("API_GetEventConfig")]
        public Dictionary<string, object> API_GetEventConfig()
        {
            return new Dictionary<string, object>
            {
                ["DefaultDuration"] = ParseRoamTime(config.roamTime),
                ["SphereRadius"] = config.sphereRadius,
                ["AvailableBiomes"] = config.biomeSettings.allowedBiomes,
                ["DefaultBiome"] = config.biomeSettings.defaultBiome,
                ["MaxConcurrentEvents"] = 1, // Currently only supports 1 roam at a time
                ["WeaponRestrictionsEnabled"] = config.weaponRestrictions != null,
                ["DecayAccelerationEnabled"] = config.decaySettings?.enableAcceleratedDecay ?? false
            };
        }

        #endregion

        // Helper method to parse roam time from config
        private int ParseRoamTime(string roamTimeStr)
        {
            if (string.IsNullOrEmpty(roamTimeStr)) return 1800; // Default 30 minutes

            try
            {
                roamTimeStr = roamTimeStr.ToLower();
                if (roamTimeStr.EndsWith("m"))
                {
                    int minutes = int.Parse(roamTimeStr.TrimEnd('m'));
                    return minutes * 60; // Convert minutes to seconds
                }
                else
                {
                    return int.Parse(roamTimeStr); // Assume it's already in seconds
                }
            }
            catch (Exception ex)
            {
                PrintError($"Error parsing roam time '{roamTimeStr}': {ex.Message}");
                return 1800; // Default 30 minutes
            }
        }

        // Called when AwakenVotingSystem plugin loads
        void OnPluginLoaded(Plugin plugin)
        {
            if (plugin?.Name == "AwakenVotingSystem")
            {
                Puts("AwakenVotingSystem detected - roam voting integration enabled");
                AwakenVotingSystem = plugin;
            }
        }

        // Called when AwakenVotingSystem plugin unloads
        void OnPluginUnloaded(Plugin plugin)
        {
            if (plugin?.Name == "AwakenVotingSystem")
            {
                Puts("AwakenVotingSystem unloaded - roam voting integration disabled");
                AwakenVotingSystem = null;
            }
        }
        #endregion

        #region Clan Core Integration
        private void AwardClanCorePoints(string clanName, string eventType)
        {
            if (ClanCores == null || !ClanCores.IsLoaded)
            {
                PrintWarning("[Roam Bubble] ClanCores plugin not found - cannot award points");
                return;
            }

            try
            {
                bool success = (bool)ClanCores.Call("API_AwardEventPoints", clanName, eventType);
                if (success)
                {
                    Puts($"[Roam Bubble] Successfully awarded clan core points to '{clanName}' for {eventType} event win");
                }
                else
                {
                    PrintWarning($"[Roam Bubble] Failed to award clan core points to '{clanName}'");
                }
            }
            catch (Exception ex)
            {
                PrintError($"[Roam Bubble] Error awarding clan core points: {ex.Message}");
            }
        }
        #endregion

        #region AwakenStats Integration
        private void AddRoamWinToStats(string clanName)
        {
            if (AwakenStats == null || !AwakenStats.IsLoaded)
            {
                PrintWarning("[Roam Bubble] AwakenStats plugin not found - cannot record event win");
                return;
            }

            try
            {
                // Add roam win for the entire clan
                AwakenStats.Call("AddEventWinForClan", clanName, "roam");
                Puts($"[Roam Bubble] Successfully recorded roam win for clan '{clanName}' in AwakenStats");
            }
            catch (Exception ex)
            {
                PrintError($"[Roam Bubble] Error recording roam win in AwakenStats: {ex.Message}");
            }
        }
        #endregion

        #region Vanish Detection

        private bool IsPlayerVanished(BasePlayer player)
        {
            if (player == null) return false;

            // Check if player is admin and vanished using common vanish plugins
            if (Vanish != null)
            {
                var isVanished = Vanish.Call("IsInvisible", player);
                if (isVanished is bool && (bool)isVanished)
                {
                    return true;
                }
            }

            // Check AdminRadar vanish
            if (AdminRadar != null)
            {
                var isVanished = AdminRadar.Call("IsInvisible", player);
                if (isVanished is bool && (bool)isVanished)
                {
                    return true;
                }
            }

            // Additional check for admin invisibility (some plugins use this)
            if (player.IsAdmin && player.limitNetworking)
            {
                return true;
            }

            return false;
        }

        #endregion

        #region Helicopter Detection and Prevention

        private bool IsPlayerOnHelicopter(BasePlayer player)
        {
            if (player == null || player.isMounted == false) return false;

            var mountedVehicle = player.GetMountedVehicle();
            if (mountedVehicle == null) return false;

            // Check for different types of helicopters
            return mountedVehicle is BaseHelicopter ||
                   mountedVehicle.PrefabName.Contains("helicopter") ||
                   mountedVehicle.PrefabName.Contains("heli") ||
                   mountedVehicle.PrefabName.Contains("minicopter") ||
                   mountedVehicle.PrefabName.Contains("scrapheli") ||
                   mountedVehicle.PrefabName.Contains("attackhelicopter");
        }

        private void EjectPlayerFromHelicopter(BasePlayer player)
        {
            if (player == null || !player.isMounted) return;

            try
            {
                var mountedVehicle = player.GetMountedVehicle();
                if (mountedVehicle != null)
                {
                    // Get a safe position to eject the player
                    Vector3 ejectPosition = GetSafeEjectPosition(player.transform.position);

                    // Dismount the player
                    player.DismountObject();

                    // Move player to safe position
                    player.MovePosition(ejectPosition);
                    player.SendNetworkUpdateImmediate();

                    // Add a small delay to ensure proper dismounting
                    Instance.timer.Once(0.1f, () => {
                        if (player != null && player.IsConnected)
                        {
                            player.Teleport(ejectPosition);
                        }
                    });

                    if (config.debug)
                    {
                        Instance.Puts($"[Roam Debug] Ejected {player.displayName} from helicopter at {ejectPosition}");
                    }
                }
            }
            catch (Exception ex)
            {
                Instance.PrintError($"Error ejecting player from helicopter: {ex.Message}");
            }
        }

        private Vector3 GetSafeEjectPosition(Vector3 originalPosition)
        {
            // Try to find a safe position on the ground
            Vector3 groundPosition = originalPosition;

            // Raycast down to find ground
            RaycastHit hit;
            if (Physics.Raycast(originalPosition, Vector3.down, out hit, 200f, LayerMask.GetMask("Terrain", "World", "Default")))
            {
                groundPosition = hit.point + Vector3.up * 2f; // Add 2 meters above ground
            }
            else
            {
                // Fallback: use terrain height
                float terrainHeight = TerrainMeta.HeightMap.GetHeight(originalPosition);
                groundPosition.y = terrainHeight + 2f;
            }

            // Ensure the position is not in water
            if (TerrainMeta.WaterMap.GetHeight(groundPosition) > groundPosition.y)
            {
                groundPosition.y = TerrainMeta.WaterMap.GetHeight(groundPosition) + 2f;
            }

            return groundPosition;
        }

        #endregion

        #region Oxide API Hooks for Other Plugins

        [HookMethod("IsRoamEventRunning")]
        public bool IsRoamEventRunning()
        {
            return activeRoams.Count > 0;
        }

        [HookMethod("IsRoamEventActive")]
        public bool IsRoamEventActive()
        {
            return activeRoams.Count > 0;
        }

        [HookMethod("GetRoamEventStatus")]
        public Dictionary<string, object> GetRoamEventStatus()
        {
            return API_GetEventStatus();
        }

        [HookMethod("GetActiveRoamEvents")]
        public List<Dictionary<string, object>> GetActiveRoamEvents()
        {
            return API_GetActiveEvents();
        }

        [HookMethod("GetRoamEventConfig")]
        public Dictionary<string, object> GetRoamEventConfig()
        {
            return API_GetEventConfig();
        }

        [HookMethod("IsPlayerInRoamBubble")]
        public bool IsPlayerInRoamBubble(string userID)
        {
            if (!playersInBubble.ContainsKey(userID)) return false;

            // Check if player is vanished
            var player = BasePlayer.FindByID(ulong.Parse(userID));
            return !IsPlayerVanished(player);
        }

        [HookMethod("GetPlayerRoamStats")]
        public Dictionary<string, object> GetPlayerRoamStats(string userID)
        {
            if (!playersInBubble.ContainsKey(userID))
            {
                return new Dictionary<string, object>
                {
                    ["inRoam"] = false,
                    ["error"] = "Player not in roam bubble"
                };
            }

            var roamBubble = playersInBubble[userID];
            var player = BasePlayer.FindByID(ulong.Parse(userID));

            // Get player stats from the roam bubble
            PlayerStats playerStats = null;
            if (roamBubble.playerStats.ContainsKey(userID))
            {
                playerStats = roamBubble.playerStats[userID];
            }

            if (playerStats == null)
            {
                return new Dictionary<string, object>
                {
                    ["inRoam"] = true,
                    ["userID"] = userID,
                    ["kills"] = 0,
                    ["deaths"] = 0,
                    ["headshots"] = 0,
                    ["damage"] = 0,
                    ["kdr"] = 0.0f,
                    ["joinTime"] = DateTime.UtcNow,
                    ["team"] = GetTeamTag(player)
                };
            }

            return new Dictionary<string, object>
            {
                ["inRoam"] = true,
                ["userID"] = userID,
                ["kills"] = playerStats.kills,
                ["deaths"] = playerStats.deaths,
                ["headshots"] = playerStats.headshots,
                ["damage"] = playerStats.damage,
                ["kdr"] = playerStats.deaths > 0 ? (float)playerStats.kills / playerStats.deaths : playerStats.kills,
                ["joinTime"] = DateTime.UtcNow, // We don't track join time in PlayerStats, so use current time
                ["team"] = GetTeamTag(player)
            };
        }

        [HookMethod("GetRoamBubbleInfo")]
        public Dictionary<string, object> GetRoamBubbleInfo()
        {
            if (activeRoams.Count == 0)
            {
                return new Dictionary<string, object>
                {
                    ["active"] = false,
                    ["error"] = "No active roam bubble"
                };
            }

            var currentBubble = activeRoams[0];
            return new Dictionary<string, object>
            {
                ["active"] = true,
                ["position"] = new Dictionary<string, float>
                {
                    ["x"] = currentBubble.transform.position.x,
                    ["y"] = currentBubble.transform.position.y,
                    ["z"] = currentBubble.transform.position.z
                },
                ["radius"] = currentBubble.innerCollider?.radius ?? config.sphereRadius,
                ["biome"] = currentBubble.biome,
                ["startTime"] = currentBubble.initialRoamTime,
                ["duration"] = currentBubble.roamTime,
                ["participantCount"] = playersInBubble.Count(kvp => !IsPlayerVanished(BasePlayer.FindByID(ulong.Parse(kvp.Key))))
            };
        }

        [HookMethod("GetRoamParticipants")]
        public List<string> GetRoamParticipants()
        {
            return playersInBubble.Keys.Where(userID => !IsPlayerVanished(BasePlayer.FindByID(ulong.Parse(userID)))).ToList();
        }

        [HookMethod("GetRoamParticipantDetails")]
        public List<Dictionary<string, object>> GetRoamParticipantDetails()
        {
            var participants = new List<Dictionary<string, object>>();

            foreach (var kvp in playersInBubble)
            {
                var player = BasePlayer.FindByID(ulong.Parse(kvp.Key));

                // Exclude vanished players from participant details
                if (IsPlayerVanished(player)) continue;

                var roamBubble = kvp.Value;
                var userID = kvp.Key;

                // Get player stats from the roam bubble
                PlayerStats playerStats = null;
                if (roamBubble.playerStats.ContainsKey(userID))
                {
                    playerStats = roamBubble.playerStats[userID];
                }

                var kills = playerStats?.kills ?? 0;
                var deaths = playerStats?.deaths ?? 0;
                var headshots = playerStats?.headshots ?? 0;
                var damage = playerStats?.damage ?? 0;

                participants.Add(new Dictionary<string, object>
                {
                    ["userID"] = userID,
                    ["name"] = player?.displayName ?? "Unknown",
                    ["kills"] = kills,
                    ["deaths"] = deaths,
                    ["headshots"] = headshots,
                    ["damage"] = damage,
                    ["kdr"] = deaths > 0 ? (float)kills / deaths : kills,
                    ["team"] = GetTeamTag(player),
                    ["joinTime"] = DateTime.UtcNow, // We don't track join time, so use current time
                    ["isOnline"] = player?.IsConnected ?? false
                });
            }

            return participants.OrderByDescending(p => (int)p["kills"]).ToList();
        }

        [HookMethod("ForceStartRoamEvent")]
        public bool ForceStartRoamEvent(Vector3 position, string biome = null)
        {
            if (activeRoams.Count > 0)
            {
                return false;
            }

            try
            {
                if (string.IsNullOrEmpty(biome))
                {
                    biome = config.biomeSettings.defaultBiome;
                }

                StartRoamEvent(biome);
                return true;
            }
            catch
            {
                return false;
            }
        }

        [HookMethod("ForceEndRoamEvent")]
        public bool ForceEndRoamEvent()
        {
            if (activeRoams.Count == 0)
            {
                return false;
            }

            try
            {
                var roamsToEnd = activeRoams.ToList();
                foreach (var roam in roamsToEnd)
                {
                    if (roam != null)
                    {
                        roam.roamTime = 0f; // This will trigger the end sequence
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region High Wall Decay System (Based on Maze Plugin)

        // Clean up all decaying high walls and their timers
        private void CleanupAllDecayingHighWalls()
        {
            foreach (var kvp in decayingHighWalls.ToList())
            {
                var decayingWall = kvp.Value;
                decayingWall.DecayTimer?.Destroy();
            }
            decayingHighWalls.Clear();

            if (config.debug)
                Puts($"[High Wall Decay] Cleaned up all decaying high walls");
        }

        // Start decay for all converted high walls when roam ends
        private void StartHighWallDecayAfterRoam()
        {
            if (!config.highWallDecaySettings.EnableHighWallDecay)
            {
                if (config.debug)
                    Puts("[High Wall Decay] High wall decay is disabled in config");
                return;
            }

            if (convertedHighWalls.Count == 0)
            {
                if (config.debug)
                    Puts("[High Wall Decay] No converted high walls to decay");
                return;
            }

            if (config.debug)
                Puts($"[High Wall Decay] Starting decay for {convertedHighWalls.Count} converted high walls after {config.highWallDecaySettings.StartDecayAfterRoamEnds} seconds");

            // Start decay after configured delay
            timer.Once(config.highWallDecaySettings.StartDecayAfterRoamEnds, () =>
            {
                var wallsToDecay = new List<BaseEntity>();

                // Find all valid converted high walls
                foreach (var wallId in convertedHighWalls.ToList())
                {
                    var wall = BaseNetworkable.serverEntities.Find(new NetworkableId(wallId)) as BaseEntity;
                    if (wall != null && !wall.IsDestroyed)
                    {
                        wallsToDecay.Add(wall);
                    }
                    else
                    {
                        // Remove destroyed walls from tracking
                        convertedHighWalls.Remove(wallId);
                    }
                }

                if (wallsToDecay.Count > 0)
                {
                    StartHighWallDecay(wallsToDecay);
                }
            });
        }

        // Start the decay process for a list of high walls
        private void StartHighWallDecay(List<BaseEntity> wallsToDecay)
        {
            if (config.highWallDecaySettings.DecayAllWallsSimultaneously)
            {
                // Start decay for all walls at the same time
                foreach (var wall in wallsToDecay)
                {
                    StartIndividualWallDecay(wall);
                }
            }
            else
            {
                // Stagger the decay start times
                for (int i = 0; i < wallsToDecay.Count; i++)
                {
                    var wall = wallsToDecay[i];
                    float delay = i * config.highWallDecaySettings.StaggerDecayStart;

                    timer.Once(delay, () =>
                    {
                        if (wall != null && !wall.IsDestroyed)
                        {
                            StartIndividualWallDecay(wall);
                        }
                    });
                }
            }

            if (config.debug)
                Puts($"[High Wall Decay] Started decay process for {wallsToDecay.Count} walls");
        }

        // Start decay for an individual wall
        private void StartIndividualWallDecay(BaseEntity wall)
        {
            if (wall == null || wall.IsDestroyed) return;

            var combatEntity = wall as BaseCombatEntity;
            if (combatEntity == null) return;

            // Create decay tracking object
            var decayingWall = new DecayingHighWall
            {
                Entity = wall,
                MaxHealth = combatEntity.MaxHealth(),
                CurrentHealth = combatEntity.Health(),
                DecayStartTime = UnityEngine.Time.realtimeSinceStartup,
                DecayDuration = config.highWallDecaySettings.HighWallDecayDuration,
                TickInterval = config.highWallDecaySettings.HighWallDecayTickInterval
            };

            // Calculate damage per tick
            float totalTicks = decayingWall.DecayDuration / decayingWall.TickInterval;
            float damagePerTick = decayingWall.MaxHealth / totalTicks;

            if (config.debug)
                Puts($"[High Wall Decay] Starting decay for wall {wall.net.ID} - Health: {decayingWall.CurrentHealth:F1}/{decayingWall.MaxHealth:F1}, Duration: {decayingWall.DecayDuration}s, Damage per tick: {damagePerTick:F1}");

            // Start decay timer
            decayingWall.DecayTimer = timer.Repeat(decayingWall.TickInterval, 0, () =>
            {
                ProcessWallDecayTick(wall.net.ID.Value, damagePerTick);
            });

            // Track the decaying wall
            decayingHighWalls[wall.net.ID.Value] = decayingWall;

            // Show message to nearby players if enabled
            if (config.highWallDecaySettings.ShowDecayMessages)
            {
                ShowDecayMessageToNearbyPlayers(wall, "High wall is starting to decay...");
            }
        }

        // Process a single decay tick for a wall
        private void ProcessWallDecayTick(ulong wallId, float damagePerTick)
        {
            if (!decayingHighWalls.TryGetValue(wallId, out var decayingWall))
                return;

            var wall = decayingWall.Entity;
            if (wall == null || wall.IsDestroyed)
            {
                // Clean up destroyed wall
                decayingWall.DecayTimer?.Destroy();
                decayingHighWalls.Remove(wallId);
                convertedHighWalls.Remove(wallId);
                return;
            }

            var combatEntity = wall as BaseCombatEntity;
            if (combatEntity == null)
            {
                // Clean up invalid entity
                decayingWall.DecayTimer?.Destroy();
                decayingHighWalls.Remove(wallId);
                convertedHighWalls.Remove(wallId);
                return;
            }

            // Apply decay damage
            float newHealth = combatEntity.Health() - damagePerTick;
            decayingWall.CurrentHealth = newHealth;

            if (newHealth <= 0)
            {
                // Wall is fully decayed, destroy it
                if (config.debug)
                    Puts($"[High Wall Decay] Wall {wallId} fully decayed, destroying");

                if (config.highWallDecaySettings.ShowDecayMessages)
                {
                    ShowDecayMessageToNearbyPlayers(wall, "High wall has fully decayed!");
                }

                // Clean up tracking
                decayingWall.DecayTimer?.Destroy();
                decayingHighWalls.Remove(wallId);
                convertedHighWalls.Remove(wallId);

                // Destroy the wall
                wall.Kill();
            }
            else
            {
                // Apply damage and update
                combatEntity.SetHealth(newHealth);
                combatEntity.SendNetworkUpdate();

                if (config.debug && UnityEngine.Time.realtimeSinceStartup % 10f < decayingWall.TickInterval) // Log every ~10 seconds
                    Puts($"[High Wall Decay] Wall {wallId} health: {newHealth:F1}/{decayingWall.MaxHealth:F1}");
            }
        }

        // Show decay message to players near the wall
        private void ShowDecayMessageToNearbyPlayers(BaseEntity wall, string message)
        {
            const float messageRadius = 50f; // Show message to players within 50m

            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player == null || !player.IsConnected) continue;

                float distance = Vector3.Distance(player.transform.position, wall.transform.position);
                if (distance <= messageRadius)
                {
                    player.ChatMessage($"<color=#ff6500>[High Wall Decay]</color> {message}");
                }
            }
        }

        #endregion
        
    }
}