using Facepunch;
using Newtonsoft.Json;
using Oxide.Core.Libraries.Covalence;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Pegasus BGrade", "Billy Joe", "1.0.0")]
    [Description("Auto upgrade players building structures as they build.")]
    public class PegasusBGrade : CovalencePlugin
    {
        #region Classes
        public class BGradeInfo
        {
            public Timer BGradeTimer;
            public int TimeLeft;
            public BuildingGrade.Enum Tier;
            public int PersonalTimeout; // Per-player timeout setting
        }
        #endregion

        #region Config
        static Configuration config;
        public class Configuration
        {
            [JsonProperty(PropertyName = "Timeout (Secs)")] public int timeout;
            public static Configuration DefaultConfig()
            {
                return new Configuration
                {
                    timeout = 60
                };
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = Configuration.DefaultConfig();
        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Defines
        private static PegasusBGrade Instance;
        public Dictionary<string, BGradeInfo> bGradePlayers = new Dictionary<string, BGradeInfo>();
        public BGradeInfo pInfo;

        // Store personal timeouts persistently
        private Dictionary<string, int> playerPersonalTimeouts = new Dictionary<string, int>();



        #region Permissions
        const string BGradePerm1 = "Pegasusbgrade.1";
        const string BGradePerm2 = "Pegasusbgrade.2";
        const string BGradePerm3 = "Pegasusbgrade.3";
        const string BGradePerm4 = "Pegasusbgrade.4";
        const string BGradePermAll = "Pegasusbgrade.all";
        const string BGradePermAdmin = "Pegasusbgrade.admin";
        #endregion
        #endregion

        #region Hooks
        void Loaded()
        {
            Instance = this;
            permission.RegisterPermission(BGradePerm1, this);
            permission.RegisterPermission(BGradePerm2, this);
            permission.RegisterPermission(BGradePerm3, this);
            permission.RegisterPermission(BGradePerm4, this);
            permission.RegisterPermission(BGradePermAll, this);
            permission.RegisterPermission(BGradePermAdmin, this);

            LoadPlayerTimeouts();
        }

        void Unload()
        {
            SavePlayerTimeouts();
        }

        private void LoadPlayerTimeouts()
        {
            playerPersonalTimeouts = Oxide.Core.Interface.Oxide.DataFileSystem.ReadObject<Dictionary<string, int>>("PegasusBGrade_PlayerTimeouts") ?? new Dictionary<string, int>();
        }

        private void SavePlayerTimeouts()
        {
            Oxide.Core.Interface.Oxide.DataFileSystem.WriteObject("PegasusBGrade_PlayerTimeouts", playerPersonalTimeouts);
        }





        void OnEntityBuilt(Planner plan, GameObject gameObject)
        {
            var player = plan?.GetOwnerPlayer();
            if (player == null) return;

            if (plan.isTypeDeployable || !player.CanBuild()) return;

            if (!bGradePlayers.TryGetValue(player.UserIDString, out pInfo)) return;

            if (!HasPermission(player, pInfo.Tier)) return;

            var buildingBlock = gameObject.GetComponent<BuildingBlock>();
            if (buildingBlock == null) return;



            Dictionary<int, int> itemsToTake = Pool.Get<Dictionary<int, int>>();
            string resourceCheck = TakeResources(player, (int)pInfo.Tier, buildingBlock, out itemsToTake);
            if (!string.IsNullOrEmpty(resourceCheck))
            {
                player.ChatMessage(resourceCheck);
                Pool.FreeUnmanaged(ref itemsToTake);
                return;
            }

            foreach (var itemToTake in itemsToTake)
            {
                if (player.inventory.Take(null, itemToTake.Key, itemToTake.Value) > 0)
                {
                    player.SendConsoleCommand("note.inv", itemToTake.Key, itemToTake.Value * -1);
                }
            }

            pInfo.TimeLeft = config.timeout;
            buildingBlock.SetGrade(pInfo.Tier);
            buildingBlock.SetHealthToMax();
            buildingBlock.StartBeingRotatable();
            buildingBlock.SendNetworkUpdate();
            buildingBlock.UpdateSkin();
            buildingBlock.ResetUpkeepTime();
            buildingBlock.GetBuilding()?.Dirty();
            Pool.FreeUnmanaged(ref itemsToTake);
        }
        #endregion

        #region Functions
        bool HasPermission(BasePlayer player, BuildingGrade.Enum tier)
        {
            if (permission.UserHasPermission(player.UserIDString, BGradePermAll)) return true;

            string perm = string.Empty;
            switch (tier)
            {
                case BuildingGrade.Enum.Wood:
                    perm = BGradePerm1;
                    break;
                case BuildingGrade.Enum.Stone:
                    perm = BGradePerm2;
                    break;
                case BuildingGrade.Enum.Metal:
                    perm = BGradePerm3;
                    break;
                case BuildingGrade.Enum.TopTier:
                    perm = BGradePerm4;
                    break;
            }

            if (permission.UserHasPermission(player.UserIDString, perm)) return true;
            return false;
        }

        string TakeResources(BasePlayer player, int playerGrade, BuildingBlock buildingBlock, out Dictionary<int, int> items)
        {
            items = new Dictionary<int, int>();

            List<ItemAmount> costToBuild = null;
            foreach (var grade in buildingBlock.blockDefinition.grades)
            {
                if (grade.gradeBase.type == (BuildingGrade.Enum)playerGrade)
                {
                    costToBuild = grade.CostToBuild();
                    break;
                }
            }

            if (costToBuild == null)
            {
                return "Error: Couldn't find cost to build for this grade.";
            }

            foreach (var itemAmount in costToBuild)
            {
                if (!items.ContainsKey(itemAmount.itemid))
                    items.Add(itemAmount.itemid, 0);

                items[itemAmount.itemid] += (int)itemAmount.amount;
            }

            var canAfford = true;
            foreach (var itemToTake in items)
            {
                if (!HasItemAmount(player, itemToTake.Key, itemToTake.Value))
                {
                    canAfford = false;
                    var itemName = ItemManager.FindItemDefinition(itemToTake.Key)?.displayName?.english ?? "Unknown Item";
                    player.ChatMessage($"<color=#7000fd>Awaken</color> Missing: {itemToTake.Value}x {itemName}");
                    break;
                }
            }

            return canAfford ? null : "You don't have enough resources to upgrade.";
        }

        bool HasItemAmount(BasePlayer player, int itemId, int itemAmount)
        {
            var count = 0;
            var allItems = Pool.Get<List<Item>>();
            player.inventory.GetAllItems(allItems);

            foreach (var item in allItems)
            {
                if (item.info.itemid == itemId)
                    count += item.amount;
            }

            Pool.FreeUnmanaged(ref allItems);
            return count >= itemAmount;
        }








        #endregion



        #region Helper Methods
        private int GetPlayerTimeout(BasePlayer player)
        {
            // Check persistent storage first
            if (playerPersonalTimeouts.TryGetValue(player.UserIDString, out int personalTimeout))
            {
                return personalTimeout;
            }

            // Check active BGrade info
            if (bGradePlayers.TryGetValue(player.UserIDString, out BGradeInfo info) && info.PersonalTimeout > 0)
            {
                return info.PersonalTimeout;
            }

            return config.timeout; // Fall back to global default
        }

        private void SetPlayerTimeout(BasePlayer player, int timeout)
        {
            // Save to persistent storage
            playerPersonalTimeouts[player.UserIDString] = timeout;
            SavePlayerTimeouts();

            // Update active BGrade info if exists
            if (bGradePlayers.TryGetValue(player.UserIDString, out BGradeInfo info))
            {
                info.PersonalTimeout = timeout;
                info.TimeLeft = timeout; // Update current session
            }
        }
        #endregion

        #region Commands
        [Command("bgrade")]
        private void bgradeCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;
            if (args.Length <= 0) { player.ChatMessage("Bad arguments given, please use /bgrade help to understand this command."); return; }



            // Check if it's a timeout value (30-3600 seconds) - now sets per-player timeout
            if (int.TryParse(args[0], out int timeoutValue) && timeoutValue >= 30 && timeoutValue <= 3600)
            {
                SetPlayerTimeout(player, timeoutValue);

                int minutes = timeoutValue / 60;
                int seconds = timeoutValue % 60;
                string timeDisplay = minutes > 0 ? $"{minutes}m {seconds}s" : $"{seconds}s";

                player.ChatMessage($"<color=#7000fd>Awaken</color> Your personal BGrade timeout set to <color=orange>{timeDisplay}</color> ({timeoutValue} seconds).");
                return;
            }

            switch (args[0])
            {
                case "0":
                    if (!bGradePlayers.TryGetValue(player.UserIDString, out pInfo)) { player.ChatMessage("<color=#7000fd>Awaken</color> You currently dont have bgrade active."); return; }
                    pInfo.BGradeTimer.Destroy();
                    bGradePlayers.Remove(player.UserIDString);
                    player.ChatMessage("<color=#7000fd>Awaken</color> Automatic upgrading is now disabled.");
                    break;

                case "1":
                case "2":
                case "3":
                case "4":
                    int grade = Convert.ToInt32(args[0]);
                    if (!HasPermission(player, (BuildingGrade.Enum)grade))
                    {
                        player.ChatMessage("<color=#7000fd>Awaken</color> You do not have permission to use bgrade for this tier.");
                        return;
                    }

                    int playerTimeout = GetPlayerTimeout(player);

                    if (bGradePlayers.TryGetValue(player.UserIDString, out pInfo))
                    {
                        pInfo.Tier = (BuildingGrade.Enum)grade;
                        pInfo.TimeLeft = playerTimeout;

                        // Restart timer if it doesn't exist
                        if (pInfo.BGradeTimer == null)
                        {
                            pInfo.BGradeTimer = timer.Every(1, () =>
                            {
                                if (player == null || !bGradePlayers.TryGetValue(player.UserIDString, out pInfo)) return;
                                pInfo.TimeLeft--;
                                if (pInfo.TimeLeft <= 0)
                                {
                                    bGradePlayers.Remove(player.UserIDString);
                                    player.ChatMessage($"<color=#7000fd>Awaken</color> Automatic upgrading has been automatically disabled.");
                                    return;
                                }
                            });
                        }
                    }
                    else
                    {
                        pInfo = new BGradeInfo()
                        {
                            TimeLeft = playerTimeout,
                            Tier = (BuildingGrade.Enum)grade,
                            PersonalTimeout = playerTimeout,
                            BGradeTimer = timer.Every(1, () =>
                            {
                                if (player == null || !bGradePlayers.TryGetValue(player.UserIDString, out pInfo)) return;
                                pInfo.TimeLeft--;
                                if (pInfo.TimeLeft <= 0)
                                {
                                    bGradePlayers.Remove(player.UserIDString);
                                    player.ChatMessage($"<color=#7000fd>Awaken</color> Automatic upgrading has been automatically disabled.");
                                    return;
                                }
                            })
                        };
                        bGradePlayers.Add(player.UserIDString, pInfo);
                    }

                    int activeMins = playerTimeout / 60;
                    int activeSecs = playerTimeout % 60;
                    string activeTimeDisplay = activeMins > 0 ? $"{activeMins}m {activeSecs}s" : $"{activeSecs}s";

                    player.ChatMessage($"<color=#7000fd>Awaken</color> Automatic upgrading is now set to grade <color=orange>{grade} ({(BuildingGrade.Enum)grade})</color>, it'll automatically disable in <color=orange>{activeTimeDisplay}</color>.");
                    break;

                case "timeout":
                case "time":
                    if (!permission.UserHasPermission(player.UserIDString, BGradePermAdmin))
                    {
                        player.ChatMessage("<color=#7000fd>Awaken</color> You don't have permission to change the global timeout.");
                        return;
                    }

                    if (args.Length < 2)
                    {
                        player.ChatMessage($"<color=#7000fd>Awaken</color> Current global default timeout: <color=orange>{config.timeout}</color> seconds");
                        player.ChatMessage($"<color=#7000fd>Awaken</color> Your personal timeout: <color=orange>{GetPlayerTimeout(player)}</color> seconds");
                        player.ChatMessage("<color=orange>Usage:</color> /bgrade timeout <seconds> (sets global default)");
                        player.ChatMessage("<color=orange>Usage:</color> /bgrade <seconds> (sets your personal timeout)");
                        return;
                    }

                    if (!int.TryParse(args[1], out int newTimeout) || newTimeout < 1)
                    {
                        player.ChatMessage("<color=#7000fd>Awaken</color> Invalid timeout value. Must be a positive number.");
                        return;
                    }

                    config.timeout = newTimeout;
                    SaveConfig();
                    player.ChatMessage($"<color=#7000fd>Awaken</color> Global default BGrade timeout changed to <color=orange>{newTimeout}</color> seconds.");
                    player.ChatMessage("<color=orange>Note:</color> This only affects new players. Existing players keep their personal timeouts.");

                    break;

                case "help":
                    string helpMessage = "<color=orange><size=16>BGrade Command Usages</size></color>\n" +
                                       "/bgrade 0 - Disables BGrade\n" +
                                       "/bgrade 1 - Upgrades to Wood upon placement\n" +
                                       "/bgrade 2 - Upgrades to Stone upon placement\n" +
                                       "/bgrade 3 - Upgrades to Metal upon placement\n" +
                                       "/bgrade 4 - Upgrades to Armoured upon placement\n" +
                                       "/bgrade <seconds> - Sets your personal BGrade timeout (30-3600 seconds)\n" +
                                       "  Example: /bgrade 180 (Your BGrade lasts 3 minutes)\n" +
                                       "  Example: /bgrade 300 (Your BGrade lasts 5 minutes)";

                    if (permission.UserHasPermission(player.UserIDString, BGradePermAdmin))
                    {
                        helpMessage += "\n\n<color=orange>Admin Commands:</color>\n" +
                                     "/bgrade timeout <seconds> - Changes global default timeout (Admin only)\n" +
                                     "  This only affects new players who haven't set personal timeouts";
                    }

                    // Show current settings
                    int currentTimeout = GetPlayerTimeout(player);
                    int currentMins = currentTimeout / 60;
                    int currentSecs = currentTimeout % 60;
                    string currentTimeDisplay = currentMins > 0 ? $"{currentMins}m {currentSecs}s" : $"{currentSecs}s";

                    helpMessage += $"\n\n<color=yellow>Your current timeout: {currentTimeDisplay}</color>";

                    if (bGradePlayers.TryGetValue(player.UserIDString, out BGradeInfo info) && info.BGradeTimer != null)
                    {
                        int timeLeft = info.TimeLeft;
                        int leftMinutes = timeLeft / 60;
                        int leftSeconds = timeLeft % 60;
                        string leftDisplay = leftMinutes > 0 ? $"{leftMinutes}m {leftSeconds}s" : $"{leftSeconds}s";
                        helpMessage += $"\n<color=green>BGrade active: {info.Tier} ({leftDisplay} remaining)</color>";
                    }

                    player.ChatMessage(helpMessage);
                    break;


            }
        }
        #endregion
    }
}